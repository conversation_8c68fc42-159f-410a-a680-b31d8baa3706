# 相机坐标系 XYZ 方向说明

## 概述
本文档详细说明了 `pixel_to_physical` 项目中使用的相机坐标系的定义和方向。

## 坐标系定义

### 1. 相机坐标系 (Camera Coordinate System)
根据代码中的定义 (`include/camera_intrinsics.h:25-29`)：

```
相机坐标系定义：
- 原点：相机光心 (Camera Optical Center)
- X轴：向右 (Right)
- Y轴：向下 (Down) 
- Z轴：向前（沿光轴方向）(Forward along optical axis)
```

### 2. 坐标轴方向详解

#### X轴 (水平方向)
- **方向**: 向右 (Right)
- **参考**: 从相机视角看，X轴正方向指向图像的右侧
- **单位**: 厘米 (cm)

#### Y轴 (垂直方向)  
- **方向**: 向下 (Down)
- **参考**: 从相机视角看，Y轴正方向指向图像的下方
- **单位**: 厘米 (cm)
- **注意**: 这与常见的数学坐标系不同，数学坐标系Y轴通常向上

#### Z轴 (深度方向)
- **方向**: 向前 (Forward)
- **参考**: 沿着相机光轴方向，远离相机为正方向
- **单位**: 厘米 (cm)
- **物理意义**: 表示目标到相机的距离

## 坐标转换公式

### 像素坐标到相机坐标转换
根据 `src/camera_intrinsics.cpp:189-197` 的实现：

```cpp
// 相机坐标系公式：
// X = (u - cx) * Z / fx
// Y = (v - cy) * Z / fy  
// Z = depth

double camera_x = (pixel_x - cx_) * depth / fx_;
double camera_y = (pixel_y - cy_) * depth / fy_;
double camera_z = depth;
```

其中：
- `(u, v)` = `(pixel_x, pixel_y)`: 像素坐标
- `(cx, cy)`: 相机主点坐标
- `(fx, fy)`: 相机焦距
- `depth`: 深度值（Z坐标）
- `(X, Y, Z)`: 相机坐标系坐标

## 实际应用示例

### 检测框坐标转换
在 `DetectionProcessor::convertPixelToCameraCoordinates()` 中：

```cpp
// 右下角 (xmax, ymax) 转换
auto [cam_x3, cam_y3, cam_z3] = camera_intrinsics_->pixelToCamera(
    det.bbox_flag.box.xmax, det.bbox_flag.box.ymax, depth);
det.camera_coords.bottom_right = CameraPoint(cam_x3, cam_y3, cam_z3);

// 左下角 (xmin, ymax) 转换  
auto [cam_x4, cam_y4, cam_z4] = camera_intrinsics_->pixelToCamera(
    det.bbox_flag.box.xmin, det.bbox_flag.box.ymax, depth);
det.camera_coords.bottom_left = CameraPoint(cam_x4, cam_y4, cam_z4);
```

## 坐标系对比

### 像素坐标系 vs 相机坐标系

| 坐标系 | 原点位置 | X轴方向 | Y轴方向 | Z轴方向 | 单位 |
|--------|----------|---------|---------|---------|------|
| 像素坐标系 | 图像左上角 | 向右 | 向下 | 无 | 像素 |
| 相机坐标系 | 相机光心 | 向右 | 向下 | 向前 | 厘米 |

### 与其他常见坐标系的区别

#### OpenCV 相机坐标系
- **相同点**: X轴向右，Z轴向前
- **不同点**: OpenCV的Y轴通常向下，与本项目一致

#### 机器人学坐标系 (ROS)
- **不同点**: ROS中Y轴通常向左，Z轴向上
- **转换**: 需要进行坐标变换

#### 计算机图形学坐标系
- **不同点**: 通常Y轴向上，Z轴向外（右手坐标系）

## 相机内参说明

### 当前配置 (calib_intrix_0717.json)
```json
"camera_matrix": {
  "fx": 832.2159704461329,  // X方向焦距
  "fy": 832.399605442942,   // Y方向焦距  
  "cx": 796.8798988886155,  // 主点X坐标
  "cy": 606.7248400771      // 主点Y坐标
}
```

### 图像属性
```json
"image_properties": {
  "width": 1600,    // 图像宽度（像素）
  "height": 1200,   // 图像高度（像素）
  "channels": 3,    // 颜色通道数
  "pixel_format": "BGR"
}
```

## 使用注意事项

### 1. 坐标系一致性
- 确保所有计算都在同一坐标系下进行
- 注意Y轴向下的特殊性

### 2. 单位统一
- 相机坐标系使用厘米 (cm)
- 物理距离也使用厘米
- 像素坐标使用像素单位

### 3. 深度值的获取
- Z坐标（深度）来自于物理距离测量
- 通过 `det.physical_distance` 获得

### 4. 坐标验证
```cpp
// 检查相机坐标是否有效
if (result.camera_coords.bottom_right.z > 0) {
    // 有效的相机坐标
    std::cout << "右下角相机坐标: (" 
              << result.camera_coords.bottom_right.x << ", "
              << result.camera_coords.bottom_right.y << ", " 
              << result.camera_coords.bottom_right.z << ") cm" << std::endl;
}
```

## 调试和可视化

### 输出格式
程序中相机坐标的输出格式：
```
相机坐标 - 左下角: (X, Y, Z) cm
相机坐标 - 右下角: (X, Y, Z) cm
```

### 坐标合理性检查
- X坐标: 负值表示目标在相机左侧，正值表示在右侧
- Y坐标: 负值表示目标在相机上方，正值表示在下方  
- Z坐标: 应该为正值，表示目标在相机前方

## 总结

本项目使用的相机坐标系遵循计算机视觉的标准约定：
- **右手坐标系**: X向右，Y向下，Z向前
- **原点**: 相机光心
- **单位**: 厘米
- **应用**: 主要用于目标检测框的3D定位

这种坐标系定义便于与像素坐标系进行转换，同时保持了与OpenCV等计算机视觉库的兼容性。

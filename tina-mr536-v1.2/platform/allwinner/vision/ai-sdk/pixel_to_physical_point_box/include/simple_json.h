#ifndef SIMPLE_JSON_H
#define SIMPLE_JSON_H

#include <string>
#include <map>
#include <vector>
#include <memory>

// 简单的JSON解析器，用于替代yaml-cpp
class SimpleJson {
public:
    enum Type {
        OBJECT,
        ARRAY,
        STRING,
        NUMBER,
        BOOLEAN,
        NULL_TYPE
    };

private:
    Type type_;
    std::string string_value_;
    double number_value_;
    bool bool_value_;
    std::map<std::string, std::shared_ptr<SimpleJson>> object_value_;
    std::vector<std::shared_ptr<SimpleJson>> array_value_;

public:
    SimpleJson(Type type = NULL_TYPE);
    
    // 静态方法用于解析JSON文件
    static SimpleJson parseFile(const std::string& filename);
    static SimpleJson parseString(const std::string& json_str);
    
    // 访问器方法
    bool isObject() const { return type_ == OBJECT; }
    bool isArray() const { return type_ == ARRAY; }
    bool isString() const { return type_ == STRING; }
    bool isNumber() const { return type_ == NUMBER; }
    bool isBool() const { return type_ == BOOLEAN; }
    bool isNull() const { return type_ == NULL_TYPE; }
    
    // 获取值
    std::string asString() const;
    double asDouble() const;
    int asInt() const;
    bool asBool() const;
    
    // 对象访问
    SimpleJson& operator[](const std::string& key);
    const SimpleJson& operator[](const std::string& key) const;
    bool hasKey(const std::string& key) const;
    
    // 数组访问
    SimpleJson& operator[](size_t index);
    const SimpleJson& operator[](size_t index) const;
    size_t size() const;
    
    // 设置值
    void setString(const std::string& value);
    void setNumber(double value);
    void setBool(bool value);
    void setObject();
    void setArray();
    
private:
    static SimpleJson parseValue(const std::string& json, size_t& pos);
    static SimpleJson parseObject(const std::string& json, size_t& pos);
    static SimpleJson parseArray(const std::string& json, size_t& pos);
    static SimpleJson parseString(const std::string& json, size_t& pos);
    static SimpleJson parseNumber(const std::string& json, size_t& pos);
    static void skipWhitespace(const std::string& json, size_t& pos);
    static std::string readFile(const std::string& filename);

    // 空的SimpleJson对象，用于返回不存在的键
    static SimpleJson null_json_;
};

#endif // SIMPLE_JSON_H

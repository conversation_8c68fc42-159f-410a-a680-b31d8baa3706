#ifndef CAMERA_INTRINSICS_H
#define CAMERA_INTRINSICS_H

#include <string>
#include <vector>
#include <memory>
#include "logger.h"
#include "simple_json.h"

#ifdef HAVE_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

/**
 * @brief 相机内参管理类
 *
 * CameraIntrinsics类负责加载和管理相机内参数据，包括相机矩阵和畸变系数。
 * 该类提供从YAML文件加载内参的功能，以及像素坐标到相机坐标系的转换方法。
 *
 * 主要功能：
 * - 从YAML文件加载相机内参
 * - 提供相机内参访问接口
 * - 像素坐标到相机坐标系的转换
 *
 * 相机坐标系定义：
 * - 原点：相机光心
 * - X轴：向右
 * - Y轴：向下
 * - Z轴：向前（沿光轴方向）
 *
 * @note 该类依赖于yaml-cpp库来解析YAML格式的内参文件
 */
class CameraIntrinsics {
private:
    // 相机内参
    double fx_;  ///< 焦距x方向
    double fy_;  ///< 焦距y方向
    double cx_;  ///< 主点x坐标
    double cy_;  ///< 主点y坐标
    
    // 畸变系数
    std::vector<double> distortion_coeffs_;  ///< 畸变系数
    
    // 图像属性
    int image_width_;   ///< 图像宽度
    int image_height_;  ///< 图像高度
    
    bool is_initialized_;  ///< 是否已初始化
    
#ifdef HAVE_YAML_CPP
    /**
     * @brief 从YAML节点加载相机内参
     *
     * @param node YAML节点
     * @return 是否成功加载
     */
    bool loadFromYamlNode(const YAML::Node& node);
#endif

    /**
     * @brief 从JSON节点加载相机内参
     *
     * @param json JSON节点
     * @return 是否成功加载
     */
    bool loadFromJsonNode(const SimpleJson& json);

public:
    /**
     * @brief 默认构造函数
     */
    CameraIntrinsics();
    
    /**
     * @brief 从YAML文件加载相机内参
     *
     * @param yaml_file YAML文件路径
     * @return 是否成功加载
     */
    bool loadFromYamlFile(const std::string& yaml_file);

    /**
     * @brief 从JSON文件加载相机内参
     *
     * @param json_file JSON文件路径
     * @return 是否成功加载
     */
    bool loadFromJsonFile(const std::string& json_file);
    
    /**
     * @brief 检查是否已初始化
     *
     * @return 是否已初始化
     */
    bool isInitialized() const { return is_initialized_; }
    
    /**
     * @brief 获取焦距x方向
     *
     * @return 焦距x方向
     */
    double getFx() const { return fx_; }
    
    /**
     * @brief 获取焦距y方向
     *
     * @return 焦距y方向
     */
    double getFy() const { return fy_; }
    
    /**
     * @brief 获取主点x坐标
     *
     * @return 主点x坐标
     */
    double getCx() const { return cx_; }
    
    /**
     * @brief 获取主点y坐标
     *
     * @return 主点y坐标
     */
    double getCy() const { return cy_; }
    
    /**
     * @brief 获取图像宽度
     *
     * @return 图像宽度
     */
    int getImageWidth() const { return image_width_; }
    
    /**
     * @brief 获取图像高度
     *
     * @return 图像高度
     */
    int getImageHeight() const { return image_height_; }
    
    /**
     * @brief 获取畸变系数
     *
     * @return 畸变系数向量
     */
    const std::vector<double>& getDistortionCoeffs() const { return distortion_coeffs_; }
    
    /**
     * @brief 像素坐标转换为相机坐标系
     *
     * 将像素坐标(u,v)和深度信息z转换为相机坐标系下的3D点(x,y,z)
     *
     * @param pixel_x 像素x坐标
     * @param pixel_y 像素y坐标
     * @param depth 深度值（单位：厘米）
     * @return 相机坐标系下的3D点(x,y,z)
     */
    std::tuple<double, double, double> pixelToCamera(int pixel_x, int pixel_y, double depth) const;
};

#endif // CAMERA_INTRINSICS_H

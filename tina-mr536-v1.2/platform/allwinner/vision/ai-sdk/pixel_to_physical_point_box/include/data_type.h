/**
 * @Autor: yaoshi,
 * @time: 2025-05-07,
 * @description: 顺造AI模型库，定义数据结构
 */
#ifndef DATA_TYPE_H_
#define DATA_TYPE_H_
#include <vector>
#include <iostream>
#include <cstdio>
#include <string>
// #include "opencv2/core/core.hpp"
// #include "opencv2/highgui/highgui.hpp"
// #include "opencv2/imgproc.hpp"
typedef struct BBox 
{
    float xmin;
    float ymin;
    float xmax;
    float ymax;
    int label;
    float score;
} BBox;


struct BBoxFlag
{
    BBox box;
    float flag; //测距标志位

    // 默认构造函数
    BBoxFlag() : flag(0.0f) {}

    // 带参数的构造函数
    BBoxFlag(const BBox& bbox, float flag_value) : box(bbox), flag(flag_value) {}
};

enum class DetectionType {
    BOX_DETECTION,    // 框检测
    POINT_DETECTION  // 点检测
};

struct DetectionInput {
    DetectionType type;           // 检测类型
    std::vector<DetectionResult> boxes;  // 检测框集合
};

// 相机坐标系点结构体
struct CameraPoint {
    double x, y, z;

    CameraPoint() : x(0.0), y(0.0), z(0.0) {}
    CameraPoint(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
};

// 相机坐标系结构体 - 存储bbox的相机坐标信息
struct CameraCoordinates {
    CameraPoint bottom_left; // 左下角相机坐标
    CameraPoint bottom_right; // 右下角相机坐标

    CameraCoordinates() = default;

    CameraCoordinates(
                     const CameraPoint& bottom_left_,
                     const CameraPoint& bottom_right_)
   
        : bottom_right(bottom_left_)
        , bottom_left(bottom_right_)
    {}
};


// 检测结果结构体
struct DetectionResult {
    BBoxFlag bbox_flag;  // 包含坐标、标签、置信度和测距标志位
    double physical_distance;
    double left_distance;
    double right_distance;
    double length;
    // 相机坐标系信息
    CameraCoordinates camera_coords;

    DetectionResult()
        : physical_distance(0.0)
        , left_distance(0.0)
        , right_distance(0.0)
        , length(0.0)
        , camera_coords()
    {}

    DetectionResult(const BBoxFlag& bbox_flag_)
        : bbox_flag(bbox_flag_)
        , physical_distance(0.0)
        , left_distance(0.0)
        , right_distance(0.0)
        , length(0.0)
        , camera_coords()
    {}

    // 便利构造函数，从BBox和flag创建
    DetectionResult(const BBox& bbox_, float flag_)
        : physical_distance(0.0)
        , left_distance(0.0)
        , right_distance(0.0)
        , length(0.0)
        , camera_coords()
    {
        bbox_flag.box = bbox_;
        bbox_flag.flag = flag_;
    }

    // 检查是否需要测距
    bool shouldMeasureDistance() const {
        return bbox_flag.flag == 1.0f;
    }
};




struct BaseData {
  /// type
  std::string type_ = "";
  /// name
  std::string name_ = "";
  /// error code
  int error_code_ = 0;
  /// error detail info
  std::string error_detail_ = "";
};

typedef struct ai_msg
{
    // uint32_t data_size;
    std::vector<BBoxFlag> bboxes;
    uint32_t model_id;
    // uint32_t core_id;
    uint64_t frameid;
    // uint64_t data;
    // uint64_t msg_num;
    /* data */
}ai_msg_t;

typedef struct ImgData: public BaseData
{
    const char* image_data_addr;
    int32_t image_height;
    int32_t image_width;
    std::string path;
    
    inline ImgData(){type_ = "ImgData";}
    inline ImgData(const char* addr, int32_t imgh, int32_t imgw)
    :image_data_addr(addr), image_height(imgh), image_width(imgw){
        type_ = "ImgData";
    }
    /* data */
}ImgData;

typedef struct ImageBboxes: public ImgData{
    std::vector<BBox> bboxes_;
    ImageBboxes(const char* addr, int32_t h, int32_t w, std::vector<BBox>bboxes)
    :ImgData(addr,h,w), bboxes_(bboxes){

    }
}ImageBboxes;


typedef struct InputParam{
    int coreid;
    uint64_t frameid;
    int updated;
    int debug;
    float threshold;
}InputParam;



#endif
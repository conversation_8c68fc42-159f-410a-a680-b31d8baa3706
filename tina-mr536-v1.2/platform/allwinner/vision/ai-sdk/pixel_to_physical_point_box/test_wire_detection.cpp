#include <iostream>
#include <vector>
#include "include/detection_processor.h"
#include "include/data_type.h"

// 简单的测试函数
void testWireDetectionWithoutGrouping() {
    std::cout << "测试新的wire detection逻辑（不使用分组）" << std::endl;
    
    // 创建测试输入
    DetectionInput input;
    input.type = DetectionType::POINT_DETECTION;
    
    // 添加几个测试点
    BBox box1 = {100.0f, 100.0f, 110.0f, 110.0f, 0, 0.9f};
    BBoxFlag bbox_flag1(box1, 1.0f);
    input.boxes.push_back(bbox_flag1);
    
    BBox box2 = {200.0f, 150.0f, 210.0f, 160.0f, 0, 0.8f};
    BBoxFlag bbox_flag2(box2, 1.0f);
    input.boxes.push_back(bbox_flag2);
    
    BBox box3 = {300.0f, 200.0f, 310.0f, 210.0f, 0, 0.7f};
    BBoxFlag bbox_flag3(box3, 1.0f);
    input.boxes.push_back(bbox_flag3);
    
    std::cout << "输入了 " << input.boxes.size() << " 个检测点" << std::endl;
    
    // 注意：这里只是展示测试结构，实际运行需要完整的DetectionProcessor实例
    // DetectionProcessor processor;
    // auto results = processor.processWireDetectionWithGrouping(input);
    // std::cout << "输出了 " << results.size() << " 个结果" << std::endl;
    
    std::cout << "测试结构创建完成" << std::endl;
}

int main() {
    testWireDetectionWithoutGrouping();
    return 0;
}

#ifndef DETECTION_PROCESSOR_H
#define DETECTION_PROCESSOR_H

#include <string>
#include <utility>
#include <memory>
#include "pixel_converter.h"
#include "data_type.h"
#include "simple_json.h"
#include "camera_intrinsics.h"
// #include "error_handling.h"
#ifdef HAVE_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

/**
 * @brief 检测结果处理器
 *
 * DetectionProcessor类负责处理目标检测的结果，将像素坐标转换为物理坐标，
 * 并计算目标的物理距离和尺寸信息。该类是整个像素到物理坐标转换系统的核心组件。
 *
 * 主要功能：
 * - 像素坐标到物理坐标的转换
 * - 物理距离计算
 * - 目标尺寸估算
 * - 无效区域处理和鲁棒性坐标查找
 * - 配置文件管理
 *
 * 坐标系说明：
 * - 输入：图像像素坐标系（左上角为原点）
 * - 输出：物理坐标系（设备底部中心为原点，单位：厘米）
 *
 * @note 该类依赖于预先计算的距离表文件和尺寸范围配置文件
 * @see PixelConverter 底层坐标转换实现
 * @see DetectionResult 检测结果数据结构
 *
 * 典型用法：
 * @code
 * DetectionProcessor processor;
 * if (processor.initializeFromConfig("../config/app_config.json")) {
 *     BBox bbox(100, 200, 200, 300, 0, 0.8f);  // xmin, ymin, xmax, ymax, label, score
 *     DetectionResult det(bbox, true);
 *     auto result = processor.processBoxDetectionResult(det);
 *     if (result.physical_distance > 0) {
 *         // 处理有效的检测结果
 *         std::cout << "距离: " << result.physical_distance << " cm" << std::endl;
 *     }
 * }
 * @endcode
 *
 * @warning 使用前必须调用initializeFromConfig()或相关初始化方法
 */
class DetectionProcessor {
private:
    SimpleJson size_ranges_config_;  ///< JSON格式的尺寸范围配置
#ifdef HAVE_YAML_CPP
    YAML::Node yaml_size_ranges_config_;  ///< YAML格式的尺寸范围配置（向后兼容）
#endif
    std::unique_ptr<PixelConverter> pixel_converter_;  ///< 像素坐标转换器
    std::unique_ptr<CameraIntrinsics> camera_intrinsics_;  ///< 相机内参
    std::shared_ptr<AppConfig> app_config_;  ///< 应用配置，包含类别名称映射

    /**
     * @brief 检查物理坐标是否有效
     *
     * 验证给定的物理坐标是否为有效值，排除无效区域标记（如-999等）。
     *
     * @param x 物理X坐标（厘米）
     * @param y 物理Y坐标（厘米）
     * @return true 坐标有效，false 坐标无效
     */
    bool isValidCoordinate(double x, double y) const;

    /**
     * @brief 在指定像素点周围搜索最近的有效物理坐标点
     *
     * 当目标像素点对应的物理坐标无效时，在其周围进行螺旋搜索，
     * 找到最近的有效物理坐标点作为替代。
     *
     * @param pixel_x 目标像素X坐标
     * @param pixel_y 目标像素Y坐标
     * @param max_search_radius 最大搜索半径（像素）
     * @return 最近的有效物理坐标对，如果未找到则返回原始无效坐标
     */
    std::pair<double, double> findNearestValidPoint(int pixel_x, int pixel_y, int max_search_radius = 50);

    /**
     * @brief 获取鲁棒的物理坐标（传统版本）
     *
     * 尝试获取指定像素点的物理坐标，如果直接转换失败，
     * 则使用搜索算法找到最近的有效坐标点。
     *
     * @param pixel_x 像素X坐标
     * @param pixel_y 像素Y坐标
     * @return 物理坐标对
     * @deprecated 推荐使用getRobustPhysicalCoordinateSafe()
     */
    std::pair<double, double> getRobustPhysicalCoordinate(int pixel_x, int pixel_y);

    // /**
    //  * @brief 获取鲁棒的物理坐标（安全版本）
    //  *
    //  * 使用错误处理机制的安全版本，提供更好的错误信息和异常处理。
    //  *
    //  * @param pixel_x 像素X坐标
    //  * @param pixel_y 像素Y坐标
    //  * @return 成功时返回物理坐标对，失败时返回错误信息
    //  */
    // ErrorHandling::Result<std::pair<double, double>> getRobustPhysicalCoordinateSafe(int pixel_x, int pixel_y);

    /**
     * @brief 计算目标尺寸（传统版本）
     *
     * 基于边界框计算目标的物理尺寸，包括长度、左下距离和右下距离。
     *
     * @param bbox 目标边界框
     * @return 尺寸元组(长度, 左下距离, 右下距离)
     * @deprecated 推荐使用calculateTargetSizeSafe()
     */
    std::tuple<double, double, double> calculateTargetSize(const BBox& bbox);

    // /**
    //  * @brief 计算目标尺寸（安全版本）
    //  *
    //  * 使用错误处理机制的安全版本，提供更好的错误信息和异常处理。
    //  *
    //  * @param bbox 目标边界框
    //  * @return 成功时返回尺寸元组，失败时返回错误信息
    //  */
    // ErrorHandling::Result<std::tuple<double, double, double>> calculateTargetSizeSafe(const BBox& bbox);

    /**
     * @brief 检查尺寸是否对指定标签合理
     *
     * 根据配置的尺寸范围，验证计算出的目标尺寸是否在该类别的合理范围内。
     *
     * @param label 目标类别标签
     * @param size_y 计算出的Y方向尺寸（厘米）
     * @return true 尺寸合理，false 尺寸不合理
     */
    bool isSizeReasonableForLabel(const std::string& label, double size_y) const;



public:
    /**
     * @brief 默认构造函数
     *
     * 创建一个未初始化的DetectionProcessor实例。
     * 使用前必须调用initializeDistanceTable()进行初始化。
     */
    DetectionProcessor() = default;

    /**
     * @brief 初始化距离表
     *
     * 加载预先计算的距离表文件，并初始化像素转换器。
     * 这是使用DetectionProcessor前的必要步骤。
     *
     * @param table_path 距离表文件的路径
     * @param config 应用配置对象，包含图像尺寸等参数
     *
     * @note 距离表文件包含像素坐标到物理坐标的映射关系
     * @warning 此方法只需调用一次，重复调用会重新初始化
     *
     * @see PixelConverter::initializeDistanceTable()
     */
    void initializeDistanceTable(const std::string& table_path, std::shared_ptr<AppConfig> config);

    /**
     * @brief 从JSON文件设置尺寸范围配置
     *
     * 从指定的JSON文件加载各类别目标的尺寸范围配置。
     *
     * @param json_file JSON配置文件路径
     *
     * @note JSON文件格式示例：
     * @code
     * {
     *   "person": {"min_size": 50, "max_size": 200},
     *   "car": {"min_size": 100, "max_size": 500}
     * }
     * @endcode
     */
    void setSizeRangesConfigFromJson(const std::string& json_file);

    /**
     * @brief 设置尺寸范围配置（JSON对象）
     *
     * 直接使用SimpleJson对象设置尺寸范围配置。
     *
     * @param config 包含尺寸范围的JSON配置对象
     */
    void setSizeRangesConfig(const SimpleJson& config);

    /**
     * @brief 初始化相机内参
     *
     * 从YAML文件加载相机内参数据。
     *
     * @param intrinsics_file 相机内参YAML文件路径
     * @return 是否成功初始化
     */
    bool initializeCameraIntrinsics(const std::string& intrinsics_file);

#ifdef HAVE_YAML_CPP
    /**
     * @brief 设置尺寸范围配置（YAML对象）
     *
     * 使用YAML格式的配置对象设置尺寸范围（向后兼容）。
     *
     * @param config YAML配置对象
     * @deprecated 推荐使用JSON格式的配置
     */
    void setSizeRangesConfig(const YAML::Node& config);
#endif

    /**
     * @brief 检查检测结果是否合理
     *
     * 对检测结果进行全面的合理性检查，包括坐标有效性、尺寸合理性等。
     *
     * @param det 检测结果对象（会被修改以添加计算结果）
     * @return 检查结果元组：(是否合理, 物理距离, 左下距离, 右下距离, 描述信息)
     *
     * @note 此方法会修改传入的DetectionResult对象，添加计算出的物理信息
     */
    std::tuple<bool, double, double, double, std::string> isDetectionReasonable(DetectionResult& det, DetectionType type);

    /**
     * @brief 将bbox像素坐标转换为相机坐标系
     *
     * 使用相机内参和物理距离，将检测框的像素坐标转换为相机坐标系下的3D坐标。
     * 转换包括bbox中心点和四个角点的坐标。
     *
     * @param det 检测结果对象，包含bbox和physical_distance信息
     * @return 是否成功转换
     *
     * @note 转换结果会直接更新到DetectionResult对象的相机坐标字段中
     */
    bool convertPixelToCameraCoordinates(DetectionResult& det);

    /**
     * @brief 处理检测结果
     *
     * 对检测结果进行完整的处理，包括坐标转换、距离计算、尺寸估算等。
     * 这是DetectionProcessor的主要接口方法。
     *
     * @param det 输入的检测结果对象
     * @return 处理后的检测结果，包含完整的物理信息
     *
     * @note 处理过程包括：
     *       1. 像素坐标转换为物理坐标
     *       2. 计算物理距离
     *       3. 估算目标尺寸
     *       4. 像素坐标转换为相机坐标系
     *       5. 生成描述信息
     *       6. 合理性验证
     */
    DetectionResult processBoxDetectionResult(DetectionResult& det, DetectionType type);

    /**
     * @brief 处理高精度电线检测结果（点检测）
     *
     * 专门处理高精度电线检测的点数据，其中bbox的左上角和右下角是同一个点。
     *
     * @param det 输入的检测结果对象，其中bbox表示点坐标
     * @return 处理后的检测结果，包含物理距离信息
     *
     * @note 对于点检测：
     *       1. 将点坐标转换为物理距离
     *       2. 转换为相机坐标系
     *       3. 不进行尺寸估算（因为是点数据）
     */
    DetectionResult processPointDetection(DetectionResult& det);

    /**
     * @brief 统一的检测结果处理接口
     *
     * 根据检测类型自动选择合适的处理方法。
     *
     * @param input 包含检测类型和检测数据的输入结构
     * @return 处理后的检测结果列表
     */
    std::vector<DetectionResult> processDetectionInput(const DetectionInput& input);

    /**
     * @brief 处理高精度电线检测的完整流程
     *
     * 处理电线检测点，计算包围所有有效点的最小外接矩形，并返回包围框的检测结果
     *
     * @param input 包含电线检测点数据的输入结构
     * @return 处理后的检测结果列表（包含包围框）
     */
    std::vector<DetectionResult> processWireDetection(
        const DetectionInput& input
    );

};

#endif // DETECTION_PROCESSOR_H
#include "../include/app_config.h"
#include <iostream>
#include <fstream>
#include <sstream>

AppConfig::AppConfig()
    : size_ranges_config("")
    , distance_table_path("")
    , calib_intrix_path("")
    , imgx_range(0, 0)
    , imgy_range(0, 0)
    , X_MIN(0)
    , X_MAX(0)
    , Y_MIN(0)
    , Y_MAX(0)
    , window_size(10)
    , max_z_diff_threshold(14.0)
    , pixel_gap_threshold(45.0)
    // , img_wh(0, 0)
{
    // 默认构造函数初始化所有成员变量为安全的默认值
    // 实际值必须从JSON配置文件中读取
}

int AppConfig::getTableWidth() const {
    return imgx_range.second - imgx_range.first + 1;
}

int AppConfig::getTableHeight() const {
    return imgy_range.second - imgy_range.first + 1;
}

// Constructor that loads from JSON file
AppConfig::AppConfig(const std::string& json_file_path) : AppConfig() {
    loadFromJson(json_file_path);
}

// Load configuration from JSON file
bool AppConfig::loadFromJson(const std::string& json_file_path) {
    try {
        SimpleJson config = SimpleJson::parseFile(json_file_path);
        if (config.isNull()) {
            std::cerr << "Error: Failed to parse JSON config file: " << json_file_path << std::endl;
            return false;
        }

        
        if (config.hasKey("size_ranges_config")) {
            size_ranges_config = config["size_ranges_config"].asString();
        }
        if (config.hasKey("distance_table_path")) {
            distance_table_path = config["distance_table_path"].asString();
        }
        
        if (config.hasKey("calib_intrix_path")) {
            calib_intrix_path = config["calib_intrix_path"].asString();
        }
        
        if (config.hasKey("X_MIN")) {
            X_MIN = config["X_MIN"].asInt();
        }
        if (config.hasKey("X_MAX")) {
            X_MAX = config["X_MAX"].asInt();
        }
        if (config.hasKey("Y_MIN")) {
            Y_MIN = config["Y_MIN"].asInt();
        }
        if (config.hasKey("Y_MAX")) {
            Y_MAX = config["Y_MAX"].asInt();
        }

        // Load range parameters
        if (config.hasKey("imgx_range")) {
            const SimpleJson& imgx_obj = config["imgx_range"];
            if (imgx_obj.hasKey("min") && imgx_obj.hasKey("max")) {
                imgx_range.first = imgx_obj["min"].asInt();
                imgx_range.second = imgx_obj["max"].asInt();
            }
        }

        if (config.hasKey("imgy_range")) {
            const SimpleJson& imgy_obj = config["imgy_range"];
            if (imgy_obj.hasKey("min") && imgy_obj.hasKey("max")) {
                imgy_range.first = imgy_obj["min"].asInt();
                imgy_range.second = imgy_obj["max"].asInt();
            }
        }

        // if (config.hasKey("img_wh")) {
        //     const SimpleJson& img_wh_obj = config["img_wh"];
        //     if (img_wh_obj.hasKey("width") && img_wh_obj.hasKey("height")) {
        //         img_wh.first = img_wh_obj["width"].asInt();
        //         img_wh.second = img_wh_obj["height"].asInt();
        //     }
        // }

        // Load class names array
        if (config.hasKey("class_names")) {
            const SimpleJson& class_names_array = config["class_names"];
            if (class_names_array.isArray()) {
                class_names.clear();
                for (size_t i = 0; i < class_names_array.size(); ++i) {
                    class_names.push_back(class_names_array[i].asString());
                }
            }
        }

        // Load wire detection grouping parameters
        if (config.hasKey("wire_detection_grouping")) {
            const SimpleJson& grouping_obj = config["wire_detection_grouping"];
            if (grouping_obj.hasKey("window_size")) {
                window_size = grouping_obj["window_size"].asInt();
            }
            if (grouping_obj.hasKey("max_z_diff_threshold")) {
                max_z_diff_threshold = grouping_obj["max_z_diff_threshold"].asDouble();
            }
            if (grouping_obj.hasKey("pixel_gap_threshold")) {
                pixel_gap_threshold = grouping_obj["pixel_gap_threshold"].asDouble();
            }
        }

        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error loading JSON config: " << e.what() << std::endl;
        return false;
    }
}

// // Save configuration to JSON file
// bool AppConfig::saveToJson(const std::string& json_file_path) const {
//     try {
//         // 由于SimpleJson API的限制，我们直接构建JSON字符串
//         std::ostringstream json_stream;
//         json_stream << "{\n";

//         // String parameters
//         json_stream << "  \"size_ranges_config\": \"" << size_ranges_config << "\",\n";
//         json_stream << "  \"distance_table_path\": \"" << distance_table_path << "\",\n";
//         json_stream << "  \"calib_yaml_path\": \"" << calib_intrix_path << "\",\n";
        

//         // Range parameters
//         json_stream << "  \"imgx_range\": {\n";
//         json_stream << "    \"min\": " << imgx_range.first << ",\n";
//         json_stream << "    \"max\": " << imgx_range.second << "\n";
//         json_stream << "  },\n";

//         json_stream << "  \"imgy_range\": {\n";
//         json_stream << "    \"min\": " << imgy_range.first << ",\n";
//         json_stream << "    \"max\": " << imgy_range.second << "\n";
//         json_stream << "  },\n";

//         json_stream << "  \"X_MIN\": " << X_MIN << ",\n";
//         json_stream << "  \"X_MAX\": " << X_MAX << ",\n";
//         json_stream << "  \"Y_MIN\": " << Y_MIN << ",\n";
//         json_stream << "  \"Y_MAX\": " << Y_MAX << ",\n";

//         json_stream << "  \"img_wh\": {\n";
//         json_stream << "    \"width\": " << img_wh.first << ",\n";
//         json_stream << "    \"height\": " << img_wh.second << "\n";
//         json_stream << "  },\n";

//         // Class names array
//         json_stream << "  \"class_names\": [\n";
//         for (size_t i = 0; i < class_names.size(); ++i) {
//             json_stream << "    \"" << class_names[i] << "\"";
//             if (i < class_names.size() - 1) {
//                 json_stream << ",";
//             }
//             json_stream << "\n";
//         }
//         json_stream << "  ]\n";
//         json_stream << "}\n";

//         // Write to file
//         std::ofstream file(json_file_path);
//         if (!file.is_open()) {
//             std::cerr << "Error: Cannot create JSON config file: " << json_file_path << std::endl;
//             return false;
//         }

//         file << json_stream.str();
//         file.close();

//         std::cout << "Successfully saved configuration to: " << json_file_path << std::endl;
//         return true;

//     } catch (const std::exception& e) {
//         std::cerr << "Error saving JSON config: " << e.what() << std::endl;
//         return false;
//     }
// }

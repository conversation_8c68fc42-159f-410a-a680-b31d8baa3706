#include "../include/simple_json.h"
#include <fstream>
#include <sstream>
#include <stdexcept>
#include <cctype>
#include <iostream>

// 静态成员定义
SimpleJson SimpleJson::null_json_(SimpleJson::NULL_TYPE);

SimpleJson::SimpleJson(Type type) 
    : type_(type), number_value_(0.0), bool_value_(false) {
}

SimpleJson SimpleJson::parseFile(const std::string& filename) {
    std::string content = readFile(filename);
    return parseString(content);
}

SimpleJson SimpleJson::parseString(const std::string& json_str) {
    size_t pos = 0;
    skipWhitespace(json_str, pos);
    return parseValue(json_str, pos);
}

std::string SimpleJson::asString() const {
    if (type_ == STRING) return string_value_;
    if (type_ == NUMBER) return std::to_string(number_value_);
    if (type_ == BOOLEAN) return bool_value_ ? "true" : "false";
    return "";
}

double SimpleJson::asDouble() const {
    if (type_ == NUMBER) return number_value_;
    if (type_ == STRING) {
        try {
            return std::stod(string_value_);
        } catch (...) {
            return 0.0;
        }
    }
    return 0.0;
}

int SimpleJson::asInt() const {
    return static_cast<int>(asDouble());
}

bool SimpleJson::asBool() const {
    if (type_ == BOOLEAN) return bool_value_;
    if (type_ == NUMBER) return number_value_ != 0.0;
    if (type_ == STRING) return !string_value_.empty();
    return false;
}

SimpleJson& SimpleJson::operator[](const std::string& key) {
    if (type_ != OBJECT) {
        setObject();
    }
    
    auto it = object_value_.find(key);
    if (it == object_value_.end()) {
        object_value_[key] = std::make_shared<SimpleJson>(NULL_TYPE);
    }
    return *object_value_[key];
}

const SimpleJson& SimpleJson::operator[](const std::string& key) const {
    if (type_ != OBJECT) {
        return null_json_;
    }
    
    auto it = object_value_.find(key);
    if (it == object_value_.end()) {
        return null_json_;
    }
    return *it->second;
}

bool SimpleJson::hasKey(const std::string& key) const {
    if (type_ != OBJECT) return false;
    return object_value_.find(key) != object_value_.end();
}

SimpleJson& SimpleJson::operator[](size_t index) {
    if (type_ != ARRAY) {
        setArray();
    }
    
    while (array_value_.size() <= index) {
        array_value_.push_back(std::make_shared<SimpleJson>(NULL_TYPE));
    }
    return *array_value_[index];
}

const SimpleJson& SimpleJson::operator[](size_t index) const {
    if (type_ != ARRAY || index >= array_value_.size()) {
        return null_json_;
    }
    return *array_value_[index];
}

size_t SimpleJson::size() const {
    if (type_ == ARRAY) return array_value_.size();
    if (type_ == OBJECT) return object_value_.size();
    return 0;
}

void SimpleJson::setString(const std::string& value) {
    type_ = STRING;
    string_value_ = value;
}

void SimpleJson::setNumber(double value) {
    type_ = NUMBER;
    number_value_ = value;
}

void SimpleJson::setBool(bool value) {
    type_ = BOOLEAN;
    bool_value_ = value;
}

void SimpleJson::setObject() {
    type_ = OBJECT;
    object_value_.clear();
}

void SimpleJson::setArray() {
    type_ = ARRAY;
    array_value_.clear();
}

SimpleJson SimpleJson::parseValue(const std::string& json, size_t& pos) {
    skipWhitespace(json, pos);
    
    if (pos >= json.length()) {
        throw std::runtime_error("Unexpected end of JSON");
    }
    
    char c = json[pos];
    
    if (c == '{') {
        return parseObject(json, pos);
    } else if (c == '[') {
        return parseArray(json, pos);
    } else if (c == '"') {
        return parseString(json, pos);
    } else if (c == 't' || c == 'f') {
        // Boolean
        if (json.substr(pos, 4) == "true") {
            pos += 4;
            SimpleJson result(BOOLEAN);
            result.setBool(true);
            return result;
        } else if (json.substr(pos, 5) == "false") {
            pos += 5;
            SimpleJson result(BOOLEAN);
            result.setBool(false);
            return result;
        }
        throw std::runtime_error("Invalid boolean value");
    } else if (c == 'n') {
        // Null
        if (json.substr(pos, 4) == "null") {
            pos += 4;
            return SimpleJson(NULL_TYPE);
        }
        throw std::runtime_error("Invalid null value");
    } else if (std::isdigit(c) || c == '-') {
        return parseNumber(json, pos);
    }
    
    throw std::runtime_error("Unexpected character: " + std::string(1, c));
}

SimpleJson SimpleJson::parseObject(const std::string& json, size_t& pos) {
    SimpleJson result(OBJECT);
    result.setObject();
    
    pos++; // skip '{'
    skipWhitespace(json, pos);
    
    if (pos < json.length() && json[pos] == '}') {
        pos++; // skip '}'
        return result;
    }
    
    while (pos < json.length()) {
        skipWhitespace(json, pos);
        
        // Parse key
        if (json[pos] != '"') {
            throw std::runtime_error("Expected string key");
        }
        SimpleJson key = parseString(json, pos);
        
        skipWhitespace(json, pos);
        
        // Expect ':'
        if (pos >= json.length() || json[pos] != ':') {
            throw std::runtime_error("Expected ':'");
        }
        pos++; // skip ':'
        
        // Parse value
        SimpleJson value = parseValue(json, pos);
        result.object_value_[key.asString()] = std::make_shared<SimpleJson>(value);
        
        skipWhitespace(json, pos);
        
        if (pos >= json.length()) {
            throw std::runtime_error("Unexpected end of JSON");
        }
        
        if (json[pos] == '}') {
            pos++; // skip '}'
            break;
        } else if (json[pos] == ',') {
            pos++; // skip ','
        } else {
            throw std::runtime_error("Expected ',' or '}'");
        }
    }
    
    return result;
}

SimpleJson SimpleJson::parseArray(const std::string& json, size_t& pos) {
    SimpleJson result(ARRAY);
    result.setArray();
    
    pos++; // skip '['
    skipWhitespace(json, pos);
    
    if (pos < json.length() && json[pos] == ']') {
        pos++; // skip ']'
        return result;
    }
    
    while (pos < json.length()) {
        SimpleJson value = parseValue(json, pos);
        result.array_value_.push_back(std::make_shared<SimpleJson>(value));
        
        skipWhitespace(json, pos);
        
        if (pos >= json.length()) {
            throw std::runtime_error("Unexpected end of JSON");
        }
        
        if (json[pos] == ']') {
            pos++; // skip ']'
            break;
        } else if (json[pos] == ',') {
            pos++; // skip ','
            skipWhitespace(json, pos);
        } else {
            throw std::runtime_error("Expected ',' or ']'");
        }
    }
    
    return result;
}

SimpleJson SimpleJson::parseString(const std::string& json, size_t& pos) {
    if (json[pos] != '"') {
        throw std::runtime_error("Expected '\"'");
    }
    
    pos++; // skip opening '"'
    std::string result;
    
    while (pos < json.length() && json[pos] != '"') {
        if (json[pos] == '\\') {
            pos++; // skip '\'
            if (pos >= json.length()) {
                throw std::runtime_error("Unexpected end of string");
            }
            
            char escaped = json[pos];
            switch (escaped) {
                case '"': result += '"'; break;
                case '\\': result += '\\'; break;
                case '/': result += '/'; break;
                case 'b': result += '\b'; break;
                case 'f': result += '\f'; break;
                case 'n': result += '\n'; break;
                case 'r': result += '\r'; break;
                case 't': result += '\t'; break;
                default:
                    result += escaped;
                    break;
            }
        } else {
            result += json[pos];
        }
        pos++;
    }
    
    if (pos >= json.length()) {
        throw std::runtime_error("Unterminated string");
    }
    
    pos++; // skip closing '"'
    
    SimpleJson json_result(STRING);
    json_result.setString(result);
    return json_result;
}

SimpleJson SimpleJson::parseNumber(const std::string& json, size_t& pos) {
    size_t start = pos;
    
    if (json[pos] == '-') {
        pos++;
    }
    
    while (pos < json.length() && (std::isdigit(json[pos]) || json[pos] == '.')) {
        pos++;
    }
    
    std::string number_str = json.substr(start, pos - start);
    double value = std::stod(number_str);
    
    SimpleJson result(NUMBER);
    result.setNumber(value);
    return result;
}

void SimpleJson::skipWhitespace(const std::string& json, size_t& pos) {
    while (pos < json.length() && std::isspace(json[pos])) {
        pos++;
    }
}

std::string SimpleJson::readFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open file: " + filename);
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    return buffer.str();
}

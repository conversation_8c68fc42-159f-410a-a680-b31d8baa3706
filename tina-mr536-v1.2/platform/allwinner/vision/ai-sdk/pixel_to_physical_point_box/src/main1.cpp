#include "../include/detection_processor.h"
#include "../include/data_type.h"
#include "../include/app_config.h"
#include "../include/logger.h"
#include <iostream>
#include <iomanip>
#include <string>
#include <vector>

void printUsage(const char* program_name) {
    std::cout << "用法: " << program_name << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  --debug     启用调试模式，显示详细日志信息" << std::endl;
    std::cout << "  --info      设置日志级别为INFO（默认）" << std::endl;
    std::cout << "  --warning   设置日志级别为WARNING" << std::endl;
    std::cout << "  --error     设置日志级别为ERROR" << std::endl;
    std::cout << "  --help      显示此帮助信息" << std::endl;
}

int main(int argc, char* argv[]) {
    try {
        // 默认日志级别为INFO
        Logger::Level log_level = Logger::INFO;

        // 解析命令行参数
        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];
            if (arg == "--debug") {
                log_level = Logger::DEBUG;
                std::cout << "启用调试模式" << std::endl;
            } else if (arg == "--info") {
                log_level = Logger::INFO;
                std::cout << "设置日志级别为INFO" << std::endl;
            } else if (arg == "--warning") {
                log_level = Logger::WARNING;
                std::cout << "设置日志级别为WARNING" << std::endl;
            } else if (arg == "--error") {
                log_level = Logger::ERROR;
                std::cout << "设置日志级别为ERROR" << std::endl;
            } else if (arg == "--help" || arg == "-h") {
                printUsage(argv[0]);
                return 0;
            } else {
                std::cerr << "未知参数: " << arg << std::endl;
                printUsage(argv[0]);
                return 1;
            }
        }

        // 设置日志级别
        Logger::setLevel(log_level);

        std::cout << "=== 像素到物理距离转换测试程序 ===" << std::endl;
        std::cout << std::endl;

        // 加载应用配置
        AppConfig config;

        // 尝试从JSON文件加载配置
        std::string json_config_path = "../config/app_config.json";
        if (config.loadFromJson(json_config_path)) {
            std::cout << "成功从JSON文件加载配置: " << json_config_path << std::endl;
        } else {
            std::cerr << "错误: 无法加载JSON配置文件: " << json_config_path << std::endl;
            std::cerr << "请确保配置文件存在且格式正确" << std::endl;
            return 1;
        }
        std::cout << std::endl;

        // 创建检测处理器实例
        DetectionProcessor processor;
        auto config_ptr = std::make_shared<AppConfig>(config);
        processor.initializeDistanceTable(config.distance_table_path, config_ptr);

        // 尝试加载JSON配置文件
        processor.setSizeRangesConfigFromJson(config.size_ranges_config);

        // 加载相机内参
        if (processor.initializeCameraIntrinsics(config.calib_intrix_path)) {
            std::cout << "成功加载相机内参: " << config.calib_intrix_path << std::endl;
        } else {
            std::cerr << "警告: 无法加载相机内参文件: " << config.calib_intrix_path << std::endl;
            std::cerr << "相机坐标转换功能将不可用" << std::endl;
        }

        // 测试1: 传统目标检测框
        std::cout << "=== 测试1: 传统目标检测框 ===" << std::endl;
        DetectionInput box_input;
        box_input.type = DetectionType::BOX_DETECTION;

        // 测试检测框1
        BBox bbox1;
        bbox1.xmin = 340; bbox1.ymin = 400; bbox1.xmax = 774; bbox1.ymax = 719;
        bbox1.label = 5; bbox1.score = 0.9f;
        BBoxFlag bbox_flag1;
        bbox_flag1.box = bbox1;
        bbox_flag1.flag = 1.0f;  // 需要测距
        box_input.boxes.push_back(bbox_flag1);

        // 测试检测框2
        BBox bbox2;
        bbox2.xmin = 100; bbox2.ymin = 200; bbox2.xmax = 300; bbox2.ymax = 400;
        bbox2.label = 3; bbox2.score = 0.8f;
        BBoxFlag bbox_flag2;
        bbox_flag2.box = bbox2;
        bbox_flag2.flag = 0.0f;  // 不需要测距
        box_input.boxes.push_back(bbox_flag2);

        // 测试检测框3
        BBox bbox3;
        bbox3.xmin = 500; bbox3.ymin = 300; bbox3.xmax = 800; bbox3.ymax = 700;
        bbox3.label = 7; bbox3.score = 0.95f;
        BBoxFlag bbox_flag3;
        bbox_flag3.box = bbox3;
        bbox_flag3.flag = 1.0f;  // 需要测距
        box_input.boxes.push_back(bbox_flag3);

        // 处理传统目标检测框
        std::vector<DetectionResult> box_results = processor.processDetectionInput(box_input);

        std::cout << "处理结果:" << std::endl;
        for (size_t i = 0; i < box_results.size(); ++i) {
            const auto& result = box_results[i];
            const auto& bbox_flag = box_input.boxes[i];

            std::cout << "检测框 #" << i+1 << ":" << std::endl;
            std::cout << "  坐标: (" << bbox_flag.box.xmin << ", " << bbox_flag.box.ymin << ", "
                      << bbox_flag.box.xmax << ", " << bbox_flag.box.ymax << ")" << std::endl;
            std::cout << "  标签: " << bbox_flag.box.label << ", 置信度: " << bbox_flag.box.score << std::endl;

            if (bbox_flag.flag > 0) {
                std::cout << "  物理距离: " << std::fixed << std::setprecision(2) << result.physical_distance << " cm" << std::endl;
                std::cout << "  左下角距离: " << result.left_distance << " cm" << std::endl;
                std::cout << "  右下角距离: " << result.right_distance << " cm" << std::endl;
                std::cout << "  目标长度: " << result.length << " cm" << std::endl;

                if (result.camera_coords.bottom_right.z > 0) {
                    std::cout << "  相机坐标系:" << std::endl;
                    std::cout << "    左下角: (" << result.camera_coords.bottom_left.x << ", "
                              << result.camera_coords.bottom_left.y << ", " << result.camera_coords.bottom_left.z << ") cm" << std::endl;
                    std::cout << "    右下角: (" << result.camera_coords.bottom_right.x << ", "
                              << result.camera_coords.bottom_right.y << ", " << result.camera_coords.bottom_right.z << ") cm" << std::endl;
                }
            } else {
                std::cout << "  此检测框不需要测距" << std::endl;
            }
            std::cout << std::endl;
        }





        std::cout <<"===================================================================="<< std::endl;
        // 测试2: 高精度电线检测点（真实数据测试）
        std::cout << "=== 测试2: 高精度电线检测点（真实数据测试） ===" << std::endl;
        DetectionInput wire_input;
        wire_input.type = DetectionType::POINT_DETECTION;

        // // 真实的电线检测点数据
        // std::vector<std::pair<int, int>> wire_points = {
        //     {582, 2}, {908, 4}, {584, 12}, {934, 76}, {1052, 116},
        //     {1064, 114}, {1074, 110}, {1036, 132}, {1046, 126}, {1062, 126},
        //     {1018, 144}, {1030, 142}, {1048, 144}, {1060, 140}, {1006, 160},
        //     {1016, 158}, {1030, 158}, {1048, 158}, {1058, 156}, {1006, 172},
        //     {1016, 174}, {1032, 174}, {662, 222}, {668, 236}, {672, 242},
        //     {712, 238}, {678, 256}, {684, 268}, {690, 274}, {694, 286},
        //     {698, 300}, {708, 304}, {714, 316}, {722, 322}, {728, 334},
        //     {740, 338}, {732, 346}, {744, 350}, {756, 354}, {750, 360},
        //     {758, 366}, {806, 364}, {818, 372}, {764, 378}, {772, 384},
        //     {824, 382}, {780, 394}, {788, 402}, {824, 400}, {794, 410},
        //     {804, 418}, {812, 426}, {820, 434}, {828, 442}, {838, 448},
        //     {846, 456}, {854, 462}, {864, 470}, {870, 478}, {880, 486},
        //     {888, 492}, {898, 500}, {906, 506}, {916, 514}, {924, 522},
        //     {934, 528}, {946, 532}, {952, 540}, {964, 548}, {972, 554},
        //     {982, 560}, {994, 566}, {1002, 570}, {1014, 578}, {1032, 588},
        //     {1044, 594}, {1052, 600}, {1064, 606}, {1076, 612}, {1084, 618},
        //     {1094, 624}, {1106, 628}, {1114, 634}, {1126, 640}, {1138, 644},
        //     {1146, 650}, {1158, 656}, {1172, 660}, {1180, 664}, {1192, 670},
        //     {1204, 676}, {1224, 684}, {1238, 690}, {1250, 692}, {1258, 698},
        //     {1270, 702}
        // };

        std::vector<std::pair<int, int>> wire_points = {
    {1052, 116}, {1064, 112}, {1076, 112}, {1036, 132}, {1046, 126},
    {1066, 128}, {1074, 124}, {1018, 144}, {1030, 142}, {1050, 146},
    {1060, 140}, {1016, 158}, {1032, 158}, {1048, 158}, {1058, 154},
    {1006, 174}, {1016, 174}, {1032, 174}, {1042, 170}, {568, 190},
    {566, 206}, {564, 220}, {564, 236}, {566, 252}, {568, 268},
    {572, 284}, {578, 302}, {872, 300}, {584, 316}, {590, 328},
    {594, 336}, {600, 348}, {892, 380}, {896, 388}, {600, 404},
    {902, 398}, {912, 404}, {1048, 398}, {568, 416}, {582, 410},
    {918, 414}, {928, 422}, {1062, 414}, {572, 426}, {582, 432},
    {594, 436}, {936, 428}, {948, 436}, {602, 442}, {616, 446},
    {630, 452}, {640, 454}, {956, 440}, {966, 444}, {982, 450},
    {998, 454}, {1010, 454}, {1064, 452}, {1078, 450}, {638, 456},
    {648, 458}, {664, 462}, {678, 468}, {690, 470}, {1004, 456},
    {1016, 456}, {1030, 456}, {1046, 456}, {1062, 456}, {552, 486},
    {564, 480}, {698, 472}, {712, 474}, {728, 478}, {744, 480},
    {758, 484}, {770, 486}, {508, 500}, {518, 496}, {534, 494},
    {548, 492}, {778, 488}, {792, 492}, {808, 496}, {822, 500},
    {834, 502}, {492, 514}, {500, 508}, {842, 506}, {854, 510},
    {868, 516}, {494, 522}, {502, 530}, {860, 534}, {868, 526},
    {510, 536}, {520, 540}, {534, 546}, {548, 550}, {810, 550},
    {824, 546}, {838, 542}, {852, 538}, {556, 552}, {568, 554},
    {582, 556}, {598, 558}, {614, 560}, {630, 560}, {646, 562},
    {662, 562}, {678, 562}, {694, 562}, {710, 562}, {726, 560},
    {742, 560}, {758, 558}, {774, 556}, {790, 552}, {802, 552}
};

//         std::vector<std::pair<int, int>> wire_points = {
//     {1052, 116}, {1062, 114}, {1076, 112}, {1034, 130}, {1046, 124},
//     {1066, 128}, {1074, 124}, {1016, 144}, {1030, 142}, {1050, 144},
//     {1062, 140}, {1016, 158}, {1032, 158}, {1046, 156}, {1016, 172},
//     {1030, 172}, {568, 224}, {564, 236}, {566, 252}, {568, 268},
//     {572, 284}, {578, 302}, {584, 316}, {590, 328}, {594, 336},
//     {602, 348}, {892, 382}, {896, 388}, {902, 398}, {1048, 398},
//     {918, 414}, {928, 422}, {570, 434}, {578, 438}, {936, 430},
//     {948, 436}, {586, 440}, {600, 444}, {614, 450}, {626, 454},
//     {956, 440}, {966, 444}, {982, 450}, {998, 452}, {1012, 454},
//     {1064, 452}, {1078, 448}, {634, 458}, {648, 462}, {664, 466},
//     {676, 470}, {1004, 456}, {1016, 456}, {1032, 456}, {1046, 456},
//     {1060, 456}, {552, 484}, {684, 472}, {696, 474}, {710, 476},
//     {728, 478}, {744, 480}, {758, 484}, {772, 486}, {506, 500},
//     {518, 494}, {532, 492}, {548, 492}, {778, 488}, {792, 490},
//     {808, 494}, {822, 498}, {836, 502}, {502, 512}, {844, 504},
//     {856, 506}, {870, 512}, {880, 516}, {506, 524}, {518, 530},
//     {528, 534}, {862, 534}, {872, 530}, {880, 522}, {526, 536},
//     {536, 538}, {550, 544}, {566, 548}, {580, 550}, {796, 550},
//     {808, 548}, {822, 546}, {838, 542}, {854, 538}, {864, 536},
//     {586, 552}, {600, 554}, {614, 554}, {630, 556}, {648, 556},
//     {664, 558}, {680, 558}, {694, 558}, {710, 558}, {726, 558},
//     {742, 556}, {758, 554}, {774, 554}, {788, 552}
// };
        // std::cout << "电线检测点数: " << wire_points.size() << std::endl;
        
        // 将点数据转换为BBoxFlag格式
        for (size_t i = 0; i < wire_points.size(); ++i) {
            BBox wire_point;
            wire_point.xmin = int(wire_points[i].first * (1600.0/1280.0));
            wire_point.ymin = int(wire_points[i].second * (1200.0/720.0));
            wire_point.xmax = int(wire_points[i].first * (1600.0/1280.0));  // 同一个点
            wire_point.ymax = int(wire_points[i].second * (1200.0/720.0));  // 同一个点
            wire_point.label = 1;  // wire类别
            wire_point.score = 0.9f;  // 统一置信度
            BBoxFlag wire_flag;
            wire_flag.box = wire_point;
            wire_flag.flag = 1.0f;  // 需要测距
            wire_input.boxes.push_back(wire_flag);
            // std::cout << "电线检测点 #" << i+1 << ": (" << wire_point.xmin << ", " << wire_point.ymin << ")" << std::endl;
        }

        // // 将点数据转换为BBoxFlag格式
        // for (size_t i = 0; i < wire_points.size(); ++i) {
        //     BBox wire_point;
        //     wire_point.xmin = wire_points[i].first;
        //     wire_point.ymin = wire_points[i].second;
        //     wire_point.xmax = wire_points[i].first;  // 同一个点
        //     wire_point.ymax = wire_points[i].second;  // 同一个点
        //     wire_point.label = 5;  // wire类别
        //     wire_point.score = 0.9f;  // 统一置信度
        //     BBoxFlag wire_flag;
        //     wire_flag.box = wire_point;
        //     wire_flag.flag = 1.0f;  // 需要测距
        //     wire_input.boxes.push_back(wire_flag);
        //     // std::cout << "电线检测点 #" << i+1 << ": (" << wire_point.xmin << ", " << wire_point.ymin << ")" << std::endl;
        // }

        // 处理高精度电线检测点
        std::vector<DetectionResult> wire_results = processor.processDetectionInput(wire_input);

        std::cout << "输入点数: " << wire_input.boxes.size() << std::endl;
        std::cout << "输出结果数: " << wire_results.size() << std::endl;
        
        for (size_t i = 0; i < wire_results.size(); ++i) {
            const DetectionResult& result = wire_results[i];
            
            std::cout << "  坐标: (" << result.bbox_flag.box.xmin << ", " << result.bbox_flag.box.ymin
                      << ", " << result.bbox_flag.box.xmax << ", " << result.bbox_flag.box.ymax << ")" << std::endl;
            std::cout << "  标签: " << result.bbox_flag.box.label << ", 置信度: " << result.bbox_flag.box.score << std::endl;
            std::cout << "  物理距离: " << std::fixed << std::setprecision(2) << result.physical_distance << " cm" << std::endl;

            if (result.camera_coords.bottom_left.z > 0) {
                std::cout << "  相机坐标: (" << result.camera_coords.bottom_left.x << ", "
                          << result.camera_coords.bottom_left.y << ", " << result.camera_coords.bottom_left.z << ") cm" << std::endl;
            }
            std::cout << std::endl;
        }

        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
#!/bin/bash

# 交叉编译脚本 for Allwinner MR536 platform (使用0-toolchains)
# 用于编译 pixel_to_physical_C++ 项目生成 .so 动态库
# 用法: ./cross_compile_0toolchain.sh [debug|release]

set -e  # 遇到错误立即退出

# 解析命令行参数
BUILD_TYPE=${1:-release}

# 验证构建类型
case $BUILD_TYPE in
    debug|release)
        ;;
    *)
        echo "错误: 未知的构建类型 '$BUILD_TYPE'"
        echo "用法: $0 [debug|release]"
        echo "  debug   - 调试模式 (包含调试信息，优化级别-O0)"
        echo "  release - 发布模式 (优化编译，-O2)"
        exit 1
        ;;
esac

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../../.." && pwd)"

print_info "=== 0-toolchains 交叉编译脚本 ==="
print_info "构建类型: $BUILD_TYPE"
print_info "脚本目录: $SCRIPT_DIR"
print_info "项目根目录: $PROJECT_ROOT"

# 设置交叉编译工具链路径
TOOLCHAIN_DIR="$PROJECT_ROOT/prebuilt/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/"
TOOLCHAIN_BIN="$TOOLCHAIN_DIR/bin"

# 检查工具链是否存在
if [ ! -d "$TOOLCHAIN_BIN" ]; then
    print_error "交叉编译工具链不存在: $TOOLCHAIN_BIN"
    exit 1
fi

print_info "使用交叉编译工具链: $TOOLCHAIN_BIN"

# 设置交叉编译环境变量
export PATH="$TOOLCHAIN_BIN:$PATH"
export STAGING_DIR="$TOOLCHAIN_DIR"
export CC="aarch64-none-linux-gnu-gcc"
export CXX="aarch64-none-linux-gnu-g++"
export AR="aarch64-none-linux-gnu-ar"
export STRIP="aarch64-none-linux-gnu-strip"
export RANLIB="aarch64-none-linux-gnu-ranlib"

# 验证工具链
print_info "验证交叉编译工具链..."
if ! command -v "$CC" &> /dev/null; then
    print_error "找不到交叉编译器: $CC"
    exit 1
fi

if ! command -v "$CXX" &> /dev/null; then
    print_error "找不到交叉编译器: $CXX"
    exit 1
fi

print_info "交叉编译器验证成功"
print_info "CC: $($CC --version | head -n1)"
print_info "CXX: $($CXX --version | head -n1)"

# 根据构建类型设置编译参数
if [ "$BUILD_TYPE" = "debug" ]; then
    print_info "配置Debug模式编译参数..."
    export CFLAGS="-O0 -g3 -march=armv8-a -mtune=cortex-a53 -fPIC"
    export CXXFLAGS="$CFLAGS -std=c++17 -Wno-psabi"
    CMAKE_BUILD_TYPE="Debug"
else
    print_info "配置Release模式编译参数..."
    export CFLAGS="-O2 -march=armv8-a -mtune=cortex-a53 -fPIC -DNDEBUG"
    export CXXFLAGS="$CFLAGS -std=c++17 -Wno-psabi"
    CMAKE_BUILD_TYPE="Release"
fi

export LDFLAGS="-L$TOOLCHAIN_DIR/aarch64-none-linux-gnu/libc/lib -L$TOOLCHAIN_DIR/lib"

print_info "编译标志:"
print_info "  CFLAGS: $CFLAGS"
print_info "  CXXFLAGS: $CXXFLAGS"
print_info "  LDFLAGS: $LDFLAGS"

# 创建构建目录 (根据构建类型命名)
BUILD_DIR="$SCRIPT_DIR/build_cross_$BUILD_TYPE"
if [ -d "$BUILD_DIR" ]; then
    print_warning "删除现有构建目录: $BUILD_DIR"
    rm -rf "$BUILD_DIR"
fi

mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

print_info "构建目录: $BUILD_DIR"

print_info "开始交叉编译..."

# 运行 CMake 配置
print_info "运行 CMake 配置..."
cmake \
    -DCMAKE_SYSTEM_NAME=Linux \
    -DCMAKE_SYSTEM_PROCESSOR=aarch64 \
    -DCMAKE_C_COMPILER="$CC" \
    -DCMAKE_CXX_COMPILER="$CXX" \
    -DCMAKE_AR="$AR" \
    -DCMAKE_STRIP="$STRIP" \
    -DCMAKE_RANLIB="$RANLIB" \
    -DCMAKE_FIND_ROOT_PATH="$TOOLCHAIN_DIR" \
    -DCMAKE_FIND_ROOT_PATH_MODE_PROGRAM=NEVER \
    -DCMAKE_FIND_ROOT_PATH_MODE_LIBRARY=ONLY \
    -DCMAKE_FIND_ROOT_PATH_MODE_INCLUDE=ONLY \
    -DCMAKE_BUILD_TYPE=$CMAKE_BUILD_TYPE \
    -DCMAKE_INSTALL_PREFIX="$BUILD_DIR/install" \
    "$SCRIPT_DIR"

if [ $? -ne 0 ]; then
    print_error "CMake 配置失败"
    exit 1
fi

# 编译
print_info "开始编译..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    print_error "编译失败"
    exit 1
fi

# 检查生成的文件
print_info "检查生成的文件..."
if [ -f "lib/libdistance_detection.so" ]; then
    print_info "成功生成动态库: lib/libdistance_detection.so"
    
    # 显示文件信息
    ls -la lib/libdistance_detection.so
    file lib/libdistance_detection.so
    
    # 检查依赖
    print_info "检查动态库依赖:"
    $TOOLCHAIN_BIN/aarch64-none-linux-gnu-readelf -d lib/libdistance_detection.so | grep NEEDED || true
    
    # 复制到项目根目录 (根据构建类型命名)
    OUTPUT_DIR="$SCRIPT_DIR/output_cross_$BUILD_TYPE"
    mkdir -p "$OUTPUT_DIR"
    cp lib/libdistance_detection.so* "$OUTPUT_DIR/"

    if [ -f "bin/distance_detection" ]; then
        cp bin/distance_detection "$OUTPUT_DIR/"
        print_info "成功生成测试程序: bin/distance_detection"
    fi

    print_info "编译产物已复制到: $OUTPUT_DIR"
    print_info "交叉编译完成！($BUILD_TYPE 模式)"
    
else
    print_error "未找到生成的动态库文件"
    exit 1
fi

print_info "编译完成，生成的文件位于: $BUILD_DIR/lib/"
print_info "输出目录: $OUTPUT_DIR"

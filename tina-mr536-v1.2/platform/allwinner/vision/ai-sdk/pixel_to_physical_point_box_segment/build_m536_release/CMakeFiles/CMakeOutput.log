The target system is: Linux -  - aarch64
The host system is: Linux - 6.8.0-40-generic - x86_64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-gcc 
Build flags: -O2;-pipe;-march=armv8-a;-mtune=cortex-a53;-fno-caller-saves;-fno-plt;-fPIC;-DNDEBUG
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"

The C compiler identification is GNU, found in "/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/3.22.1/CompilerIdC/a.out"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ 
Build flags: -O2;-pipe;-march=armv8-a;-mtune=cortex-a53;-fno-caller-saves;-fno-plt;-fPIC;-DNDEBUG;-std=c++17;-Wno-psabi
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"

The CXX compiler identification is GNU, found in "/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/3.22.1/CompilerIdCXX/a.out"

Detecting C compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_58e2a/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_58e2a.dir/build.make CMakeFiles/cmTC_58e2a.dir/build
gmake[1]: Entering directory '/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o
/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-gcc   -O2 -pipe -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fPIC -DNDEBUG    -v -o CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-3.22/Modules/CMakeCCompilerABI.c
Reading specs from /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/specs
COLLECT_GCC=aarch64-openwrt-linux-gnu-gcc.bin
Target: aarch64-openwrt-linux-gnu
Configured with: /home/<USER>/workspace/tina/out/mr813/evb2/openwrt/build_dir/toolchain/gcc-13.2.0/configure --with-bugurl=http://bugs.openwrt.org/ --with-pkgversion='OpenWrt GCC 13.2.0 r16744+1-c948de5403' --prefix=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain --build=x86_64-pc-linux-gnu --host=x86_64-pc-linux-gnu --target=aarch64-openwrt-linux-gnu --with-gnu-ld --enable-target-optspace --enable-libgomp --enable-libsanitizer --disable-libmudflap --disable-multilib --disable-libmpx --disable-nls --disable-libssp --without-isl --without-cloog --with-host-libstdcxx=-lstdc++ --without-zstd --with-gmp=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpfr=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpc=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --disable-decimal-float --with-diagnostics-color=auto-if-env --enable-__cxa_atexit --enable-libstdcxx-dual-abi --with-default-libstdcxx-abi=new --with-arch=armv8-a CFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' CXXFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' 'CFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'CXXFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'GOCFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' --with-headers=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/include --enable-languages=c,c++,fortran --enable-shared --enable-threads --with-slibdir=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/lib --enable-plugins --enable-lto --with-libelf=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 13.2.0 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) 
COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-v' '-o' 'CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_58e2a.dir/'
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/cc1 -quiet -v -iprefix /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/ -isysroot /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//.. -idirafter /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/include -D NDEBUG /usr/share/cmake-3.22/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_58e2a.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -march=armv8-a -mtune=cortex-a53 -mlittle-endian -mabi=lp64 -O2 -version -fno-caller-saves -fno-plt -fPIC -o - |
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o
GNU assembler version 2.42 (aarch64-openwrt-linux-gnu) using BFD version (GNU Binutils) 2.42
GNU C17 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) version 13.2.0 (aarch64-openwrt-linux-gnu)
	compiled by GNU C version 5.5.0 20171010, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version none
GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include"
ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed"
ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include"
ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include"
ignoring nonexistent directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/include"
#include "..." search starts here:
#include <...> search starts here:
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include
End of search list.
Compiler executable checksum: f3ecea59216044470cf2ab3c5f81be2a
COMPILER_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/
LIBRARY_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib/
COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-v' '-o' 'CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.'
Linking C executable cmTC_58e2a
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_58e2a.dir/link.txt --verbose=1
/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-gcc -O2 -pipe -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fPIC -DNDEBUG  -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -Wl,-rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -v CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o -o cmTC_58e2a 
Reading specs from /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/specs
COLLECT_GCC=aarch64-openwrt-linux-gnu-gcc.bin
COLLECT_LTO_WRAPPER=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/lto-wrapper
Target: aarch64-openwrt-linux-gnu
Configured with: /home/<USER>/workspace/tina/out/mr813/evb2/openwrt/build_dir/toolchain/gcc-13.2.0/configure --with-bugurl=http://bugs.openwrt.org/ --with-pkgversion='OpenWrt GCC 13.2.0 r16744+1-c948de5403' --prefix=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain --build=x86_64-pc-linux-gnu --host=x86_64-pc-linux-gnu --target=aarch64-openwrt-linux-gnu --with-gnu-ld --enable-target-optspace --enable-libgomp --enable-libsanitizer --disable-libmudflap --disable-multilib --disable-libmpx --disable-nls --disable-libssp --without-isl --without-cloog --with-host-libstdcxx=-lstdc++ --without-zstd --with-gmp=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpfr=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpc=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --disable-decimal-float --with-diagnostics-color=auto-if-env --enable-__cxa_atexit --enable-libstdcxx-dual-abi --with-default-libstdcxx-abi=new --with-arch=armv8-a CFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' CXXFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' 'CFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'CXXFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'GOCFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' --with-headers=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/include --enable-languages=c,c++,fortran --enable-shared --enable-threads --with-slibdir=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/lib --enable-plugins --enable-lto --with-libelf=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 13.2.0 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) 
COMPILER_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/
LIBRARY_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib/
COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib' '-v' '-o' 'cmTC_58e2a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_58e2a.'
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/collect2 -plugin /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/liblto_plugin.so -plugin-opt=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccSigkSV.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s --sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//.. --eh-frame-hdr -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_58e2a /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crt1.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crti.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -L /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib -rpath-link /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0 -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib -rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../usr/lib -rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o -lgcc_s -lc -lgcc_s /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crtn.o
COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib' '-v' '-o' 'cmTC_58e2a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_58e2a.'
gmake[1]: Leaving directory '/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/CMakeTmp'



Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include]
    add: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed]
    add: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include]
    add: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include]
  end of search list found
  collapse include dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include]
  collapse include dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed]
  collapse include dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/sys-include]
  collapse include dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/include]
  implicit include dirs: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/sys-include;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(aarch64-openwrt-linux-gnu-ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_58e2a/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_58e2a.dir/build.make CMakeFiles/cmTC_58e2a.dir/build]
  ignore line: [gmake[1]: Entering directory '/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/CMakeTmp']
  ignore line: [Building C object CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o]
  ignore line: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-gcc   -O2 -pipe -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fPIC -DNDEBUG    -v -o CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-3.22/Modules/CMakeCCompilerABI.c]
  ignore line: [Reading specs from /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/specs]
  ignore line: [COLLECT_GCC=aarch64-openwrt-linux-gnu-gcc.bin]
  ignore line: [Target: aarch64-openwrt-linux-gnu]
  ignore line: [Configured with: /home/<USER>/workspace/tina/out/mr813/evb2/openwrt/build_dir/toolchain/gcc-13.2.0/configure --with-bugurl=http://bugs.openwrt.org/ --with-pkgversion='OpenWrt GCC 13.2.0 r16744+1-c948de5403' --prefix=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain --build=x86_64-pc-linux-gnu --host=x86_64-pc-linux-gnu --target=aarch64-openwrt-linux-gnu --with-gnu-ld --enable-target-optspace --enable-libgomp --enable-libsanitizer --disable-libmudflap --disable-multilib --disable-libmpx --disable-nls --disable-libssp --without-isl --without-cloog --with-host-libstdcxx=-lstdc++ --without-zstd --with-gmp=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpfr=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpc=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --disable-decimal-float --with-diagnostics-color=auto-if-env --enable-__cxa_atexit --enable-libstdcxx-dual-abi --with-default-libstdcxx-abi=new --with-arch=armv8-a CFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' CXXFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' 'CFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'CXXFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'GOCFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' --with-headers=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/include --enable-languages=c,c++,fortran --enable-shared --enable-threads --with-slibdir=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/lib --enable-plugins --enable-lto --with-libelf=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host]
  ignore line: [Thread model: posix]
  ignore line: [Supported LTO compression algorithms: zlib]
  ignore line: [gcc version 13.2.0 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) ]
  ignore line: [COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-v' '-o' 'CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_58e2a.dir/']
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/cc1 -quiet -v -iprefix /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/ -isysroot /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//.. -idirafter /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/include -D NDEBUG /usr/share/cmake-3.22/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_58e2a.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -march=armv8-a -mtune=cortex-a53 -mlittle-endian -mabi=lp64 -O2 -version -fno-caller-saves -fno-plt -fPIC -o - |]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o]
  ignore line: [GNU assembler version 2.42 (aarch64-openwrt-linux-gnu) using BFD version (GNU Binutils) 2.42]
  ignore line: [GNU C17 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) version 13.2.0 (aarch64-openwrt-linux-gnu)]
  ignore line: [	compiled by GNU C version 5.5.0 20171010  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version none]
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include"]
  ignore line: [ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed"]
  ignore line: [ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include"]
  ignore line: [ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include"]
  ignore line: [ignoring nonexistent directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include]
  ignore line: [End of search list.]
  ignore line: [Compiler executable checksum: f3ecea59216044470cf2ab3c5f81be2a]
  ignore line: [COMPILER_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/]
  ignore line: [LIBRARY_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib/]
  ignore line: [COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-v' '-o' 'CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.']
  ignore line: [Linking C executable cmTC_58e2a]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_58e2a.dir/link.txt --verbose=1]
  ignore line: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-gcc -O2 -pipe -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fPIC -DNDEBUG  -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -Wl -rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -v CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o -o cmTC_58e2a ]
  ignore line: [Reading specs from /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/specs]
  ignore line: [COLLECT_GCC=aarch64-openwrt-linux-gnu-gcc.bin]
  ignore line: [COLLECT_LTO_WRAPPER=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/lto-wrapper]
  ignore line: [Target: aarch64-openwrt-linux-gnu]
  ignore line: [Configured with: /home/<USER>/workspace/tina/out/mr813/evb2/openwrt/build_dir/toolchain/gcc-13.2.0/configure --with-bugurl=http://bugs.openwrt.org/ --with-pkgversion='OpenWrt GCC 13.2.0 r16744+1-c948de5403' --prefix=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain --build=x86_64-pc-linux-gnu --host=x86_64-pc-linux-gnu --target=aarch64-openwrt-linux-gnu --with-gnu-ld --enable-target-optspace --enable-libgomp --enable-libsanitizer --disable-libmudflap --disable-multilib --disable-libmpx --disable-nls --disable-libssp --without-isl --without-cloog --with-host-libstdcxx=-lstdc++ --without-zstd --with-gmp=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpfr=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpc=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --disable-decimal-float --with-diagnostics-color=auto-if-env --enable-__cxa_atexit --enable-libstdcxx-dual-abi --with-default-libstdcxx-abi=new --with-arch=armv8-a CFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' CXXFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' 'CFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'CXXFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'GOCFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' --with-headers=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/include --enable-languages=c,c++,fortran --enable-shared --enable-threads --with-slibdir=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/lib --enable-plugins --enable-lto --with-libelf=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host]
  ignore line: [Thread model: posix]
  ignore line: [Supported LTO compression algorithms: zlib]
  ignore line: [gcc version 13.2.0 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) ]
  ignore line: [COMPILER_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/]
  ignore line: [LIBRARY_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib/]
  ignore line: [COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib' '-v' '-o' 'cmTC_58e2a' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_58e2a.']
  link line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/collect2 -plugin /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/liblto_plugin.so -plugin-opt=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccSigkSV.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s --sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//.. --eh-frame-hdr -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_58e2a /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crt1.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crti.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -L /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib -rpath-link /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0 -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib -rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../usr/lib -rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o -lgcc_s -lc -lgcc_s /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crtn.o]
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccSigkSV.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib/ld-linux-aarch64.so.1] ==> ignore
    arg [-X] ==> ignore
    arg [-EL] ==> ignore
    arg [-maarch64linux] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_58e2a] ==> ignore
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crt1.o] ==> obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crt1.o]
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crti.o] ==> obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crti.o]
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o] ==> obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o]
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib]
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib]
    arg [-rpath-link] ==> ignore
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib] ==> ignore
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0]
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc]
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib]
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib]
    arg [-rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../usr/lib] ==> ignore
    arg [-rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib] ==> ignore
    arg [CMakeFiles/cmTC_58e2a.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lc] ==> lib [c]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o] ==> obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o]
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crtn.o] ==> obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crtn.o]
  collapse obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crt1.o] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crt1.o]
  collapse obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crti.o] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crti.o]
  collapse obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o]
  collapse obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o]
  collapse obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crtn.o] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crtn.o]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib]
  implicit libs: [gcc_s;c;gcc_s]
  implicit objs: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crt1.o;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crti.o;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crtn.o]
  implicit dirs: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_ba038/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_ba038.dir/build.make CMakeFiles/cmTC_ba038.dir/build
gmake[1]: Entering directory '/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o
/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++   -O2 -pipe -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fPIC -DNDEBUG -std=c++17 -Wno-psabi    -v -o CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp
Reading specs from /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/specs
COLLECT_GCC=aarch64-openwrt-linux-gnu-g++.bin
Target: aarch64-openwrt-linux-gnu
Configured with: /home/<USER>/workspace/tina/out/mr813/evb2/openwrt/build_dir/toolchain/gcc-13.2.0/configure --with-bugurl=http://bugs.openwrt.org/ --with-pkgversion='OpenWrt GCC 13.2.0 r16744+1-c948de5403' --prefix=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain --build=x86_64-pc-linux-gnu --host=x86_64-pc-linux-gnu --target=aarch64-openwrt-linux-gnu --with-gnu-ld --enable-target-optspace --enable-libgomp --enable-libsanitizer --disable-libmudflap --disable-multilib --disable-libmpx --disable-nls --disable-libssp --without-isl --without-cloog --with-host-libstdcxx=-lstdc++ --without-zstd --with-gmp=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpfr=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpc=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --disable-decimal-float --with-diagnostics-color=auto-if-env --enable-__cxa_atexit --enable-libstdcxx-dual-abi --with-default-libstdcxx-abi=new --with-arch=armv8-a CFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' CXXFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' 'CFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'CXXFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'GOCFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' --with-headers=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/include --enable-languages=c,c++,fortran --enable-shared --enable-threads --with-slibdir=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/lib --enable-plugins --enable-lto --with-libelf=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 13.2.0 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) 
COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-std=c++17' '-Wno-psabi' '-v' '-o' 'CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_ba038.dir/'
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/cc1plus -quiet -v -iprefix /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/ -isysroot /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//.. -D_GNU_SOURCE -idirafter /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/include -D NDEBUG /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_ba038.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -march=armv8-a -mtune=cortex-a53 -mlittle-endian -mabi=lp64 -O2 -Wno-psabi -std=c++17 -version -fno-caller-saves -fno-plt -fPIC -o - |
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o
GNU assembler version 2.42 (aarch64-openwrt-linux-gnu) using BFD version (GNU Binutils) 2.42
GNU C++17 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) version 13.2.0 (aarch64-openwrt-linux-gnu)
	compiled by GNU C version 5.5.0 20171010, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version none
GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0"
ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/aarch64-openwrt-linux-gnu"
ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/backward"
ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include"
ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed"
ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include"
ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include"
ignoring nonexistent directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/include"
#include "..." search starts here:
#include <...> search starts here:
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/aarch64-openwrt-linux-gnu
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/backward
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include
End of search list.
Compiler executable checksum: 89c4e1a1bbe1d0a5e4b077477148c4d3
COMPILER_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/
LIBRARY_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib/
COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-std=c++17' '-Wno-psabi' '-v' '-o' 'CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.'
Linking CXX executable cmTC_ba038
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ba038.dir/link.txt --verbose=1
/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ -O2 -pipe -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fPIC -DNDEBUG -std=c++17 -Wno-psabi  -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -Wl,-rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -v CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_ba038 
Reading specs from /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/specs
COLLECT_GCC=aarch64-openwrt-linux-gnu-g++.bin
COLLECT_LTO_WRAPPER=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/lto-wrapper
Target: aarch64-openwrt-linux-gnu
Configured with: /home/<USER>/workspace/tina/out/mr813/evb2/openwrt/build_dir/toolchain/gcc-13.2.0/configure --with-bugurl=http://bugs.openwrt.org/ --with-pkgversion='OpenWrt GCC 13.2.0 r16744+1-c948de5403' --prefix=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain --build=x86_64-pc-linux-gnu --host=x86_64-pc-linux-gnu --target=aarch64-openwrt-linux-gnu --with-gnu-ld --enable-target-optspace --enable-libgomp --enable-libsanitizer --disable-libmudflap --disable-multilib --disable-libmpx --disable-nls --disable-libssp --without-isl --without-cloog --with-host-libstdcxx=-lstdc++ --without-zstd --with-gmp=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpfr=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpc=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --disable-decimal-float --with-diagnostics-color=auto-if-env --enable-__cxa_atexit --enable-libstdcxx-dual-abi --with-default-libstdcxx-abi=new --with-arch=armv8-a CFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' CXXFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' 'CFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'CXXFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'GOCFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' --with-headers=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/include --enable-languages=c,c++,fortran --enable-shared --enable-threads --with-slibdir=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/lib --enable-plugins --enable-lto --with-libelf=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host
Thread model: posix
Supported LTO compression algorithms: zlib
gcc version 13.2.0 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) 
COMPILER_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/
LIBRARY_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib/
COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-std=c++17' '-Wno-psabi' '-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib' '-v' '-o' 'cmTC_ba038' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_ba038.'
 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/collect2 -plugin /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/liblto_plugin.so -plugin-opt=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccNtpTHb.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s --sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//.. --eh-frame-hdr -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_ba038 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crt1.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crti.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -L /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib -rpath-link /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0 -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib -rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../usr/lib -rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lc -lgcc_s /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crtn.o
COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-std=c++17' '-Wno-psabi' '-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib' '-v' '-o' 'cmTC_ba038' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_ba038.'
gmake[1]: Leaving directory '/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/CMakeTmp'



Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0]
    add: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/aarch64-openwrt-linux-gnu]
    add: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/backward]
    add: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include]
    add: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed]
    add: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include]
    add: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include]
  end of search list found
  collapse include dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/include/c++/13.2.0]
  collapse include dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/aarch64-openwrt-linux-gnu] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/include/c++/13.2.0/aarch64-openwrt-linux-gnu]
  collapse include dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/backward] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/include/c++/13.2.0/backward]
  collapse include dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include]
  collapse include dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed]
  collapse include dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/sys-include]
  collapse include dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/include]
  implicit include dirs: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/include/c++/13.2.0;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/include/c++/13.2.0/aarch64-openwrt-linux-gnu;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/include/c++/13.2.0/backward;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/sys-include;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(aarch64-openwrt-linux-gnu-ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_ba038/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_ba038.dir/build.make CMakeFiles/cmTC_ba038.dir/build]
  ignore line: [gmake[1]: Entering directory '/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/CMakeTmp']
  ignore line: [Building CXX object CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++   -O2 -pipe -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fPIC -DNDEBUG -std=c++17 -Wno-psabi    -v -o CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [Reading specs from /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/specs]
  ignore line: [COLLECT_GCC=aarch64-openwrt-linux-gnu-g++.bin]
  ignore line: [Target: aarch64-openwrt-linux-gnu]
  ignore line: [Configured with: /home/<USER>/workspace/tina/out/mr813/evb2/openwrt/build_dir/toolchain/gcc-13.2.0/configure --with-bugurl=http://bugs.openwrt.org/ --with-pkgversion='OpenWrt GCC 13.2.0 r16744+1-c948de5403' --prefix=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain --build=x86_64-pc-linux-gnu --host=x86_64-pc-linux-gnu --target=aarch64-openwrt-linux-gnu --with-gnu-ld --enable-target-optspace --enable-libgomp --enable-libsanitizer --disable-libmudflap --disable-multilib --disable-libmpx --disable-nls --disable-libssp --without-isl --without-cloog --with-host-libstdcxx=-lstdc++ --without-zstd --with-gmp=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpfr=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpc=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --disable-decimal-float --with-diagnostics-color=auto-if-env --enable-__cxa_atexit --enable-libstdcxx-dual-abi --with-default-libstdcxx-abi=new --with-arch=armv8-a CFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' CXXFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' 'CFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'CXXFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'GOCFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' --with-headers=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/include --enable-languages=c,c++,fortran --enable-shared --enable-threads --with-slibdir=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/lib --enable-plugins --enable-lto --with-libelf=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host]
  ignore line: [Thread model: posix]
  ignore line: [Supported LTO compression algorithms: zlib]
  ignore line: [gcc version 13.2.0 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) ]
  ignore line: [COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-std=c++17' '-Wno-psabi' '-v' '-o' 'CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_ba038.dir/']
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/cc1plus -quiet -v -iprefix /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/ -isysroot /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//.. -D_GNU_SOURCE -idirafter /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/include -D NDEBUG /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_ba038.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -march=armv8-a -mtune=cortex-a53 -mlittle-endian -mabi=lp64 -O2 -Wno-psabi -std=c++17 -version -fno-caller-saves -fno-plt -fPIC -o - |]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/as -v -EL -march=armv8-a -mabi=lp64 -o CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [GNU assembler version 2.42 (aarch64-openwrt-linux-gnu) using BFD version (GNU Binutils) 2.42]
  ignore line: [GNU C++17 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) version 13.2.0 (aarch64-openwrt-linux-gnu)]
  ignore line: [	compiled by GNU C version 5.5.0 20171010  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version none]
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0"]
  ignore line: [ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/aarch64-openwrt-linux-gnu"]
  ignore line: [ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/backward"]
  ignore line: [ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include"]
  ignore line: [ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed"]
  ignore line: [ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include"]
  ignore line: [ignoring duplicate directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/../../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include"]
  ignore line: [ignoring nonexistent directory "/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/aarch64-openwrt-linux-gnu]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include/c++/13.2.0/backward]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/include-fixed]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/sys-include]
  ignore line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/include]
  ignore line: [End of search list.]
  ignore line: [Compiler executable checksum: 89c4e1a1bbe1d0a5e4b077477148c4d3]
  ignore line: [COMPILER_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/]
  ignore line: [LIBRARY_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib/]
  ignore line: [COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-std=c++17' '-Wno-psabi' '-v' '-o' 'CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.']
  ignore line: [Linking CXX executable cmTC_ba038]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ba038.dir/link.txt --verbose=1]
  ignore line: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ -O2 -pipe -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fPIC -DNDEBUG -std=c++17 -Wno-psabi  -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -Wl -rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -v CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_ba038 ]
  ignore line: [Reading specs from /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/specs]
  ignore line: [COLLECT_GCC=aarch64-openwrt-linux-gnu-g++.bin]
  ignore line: [COLLECT_LTO_WRAPPER=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/lto-wrapper]
  ignore line: [Target: aarch64-openwrt-linux-gnu]
  ignore line: [Configured with: /home/<USER>/workspace/tina/out/mr813/evb2/openwrt/build_dir/toolchain/gcc-13.2.0/configure --with-bugurl=http://bugs.openwrt.org/ --with-pkgversion='OpenWrt GCC 13.2.0 r16744+1-c948de5403' --prefix=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain --build=x86_64-pc-linux-gnu --host=x86_64-pc-linux-gnu --target=aarch64-openwrt-linux-gnu --with-gnu-ld --enable-target-optspace --enable-libgomp --enable-libsanitizer --disable-libmudflap --disable-multilib --disable-libmpx --disable-nls --disable-libssp --without-isl --without-cloog --with-host-libstdcxx=-lstdc++ --without-zstd --with-gmp=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpfr=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --with-mpc=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host --disable-decimal-float --with-diagnostics-color=auto-if-env --enable-__cxa_atexit --enable-libstdcxx-dual-abi --with-default-libstdcxx-abi=new --with-arch=armv8-a CFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' CXXFLAGS='-O2 -I/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host/include  -pipe' 'CFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'CXXFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' 'GOCFLAGS_FOR_TARGET=-Os -pipe -fno-caller-saves -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fhonour-copts -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wformat -Werror=format-security -D_FORTIFY_SOURCE=2 -Wl,-z,now -Wl,-z,relro' --with-headers=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/include --enable-languages=c,c++,fortran --enable-shared --enable-threads --with-slibdir=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/toolchain/lib --enable-plugins --enable-lto --with-libelf=/home/<USER>/workspace/tina/openwrt/openwrt/staging_dir/host]
  ignore line: [Thread model: posix]
  ignore line: [Supported LTO compression algorithms: zlib]
  ignore line: [gcc version 13.2.0 (OpenWrt GCC 13.2.0 r16744+1-c948de5403) ]
  ignore line: [COMPILER_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/bin/]
  ignore line: [LIBRARY_PATH=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib/]
  ignore line: [COLLECT_GCC_OPTIONS='--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..' '-O2' '-pipe' '-march=armv8-a' '-mtune=cortex-a53' '-fno-caller-saves' '-fno-plt' '-fPIC' '-D' 'NDEBUG' '-std=c++17' '-Wno-psabi' '-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib' '-v' '-o' 'cmTC_ba038' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64' '-dumpdir' 'cmTC_ba038.']
  link line: [ /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/collect2 -plugin /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/liblto_plugin.so -plugin-opt=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccNtpTHb.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s --sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//.. --eh-frame-hdr -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_ba038 /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crt1.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crti.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -L /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib -rpath-link /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0 -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib -rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../usr/lib -rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lc -lgcc_s /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crtn.o]
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../libexec/gcc/aarch64-openwrt-linux-gnu/13.2.0/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccNtpTHb.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [--sysroot=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//..] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib/ld-linux-aarch64.so.1] ==> ignore
    arg [-X] ==> ignore
    arg [-EL] ==> ignore
    arg [-maarch64linux] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_ba038] ==> ignore
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crt1.o] ==> obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crt1.o]
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crti.o] ==> obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crti.o]
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o] ==> obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o]
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib]
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib]
    arg [-rpath-link] ==> ignore
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib] ==> ignore
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0]
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc]
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib]
    arg [-L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib] ==> dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib]
    arg [-rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib:/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../usr/lib] ==> ignore
    arg [-rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib] ==> ignore
    arg [CMakeFiles/cmTC_ba038.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lm] ==> lib [m]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lc] ==> lib [c]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o] ==> obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o]
    arg [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crtn.o] ==> obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crtn.o]
  collapse obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crt1.o] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crt1.o]
  collapse obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crti.o] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crti.o]
  collapse obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o]
  collapse obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o]
  collapse obj [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib/crtn.o] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crtn.o]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/../lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/../../../../aarch64-openwrt-linux-gnu/lib] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib]
  collapse library dir [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin//../lib] ==> [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib]
  implicit libs: [stdc++;m;gcc_s;c;gcc_s]
  implicit objs: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crt1.o;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crti.o;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtbegin.o;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0/crtend.o;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib/crtn.o]
  implicit dirs: [/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/usr/lib;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc/aarch64-openwrt-linux-gnu/13.2.0;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib/gcc;/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/lib]
  implicit fwks: []



# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile CXX with /home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++
CXX_DEFINES = -Ddetection_processor_EXPORTS

CXX_INCLUDES = -I/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/include

CXX_FLAGS = -O2 -pipe -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fPIC -DNDEBUG -std=c++17 -Wno-psabi -O3 -DNDEBUG -fPIC -std=gnu++17


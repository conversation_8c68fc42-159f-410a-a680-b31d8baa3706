# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build/CMakeFiles /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named detection_processor

# Build rule for target.
detection_processor: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 detection_processor
.PHONY : detection_processor

# fast build rule for target.
detection_processor/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/build
.PHONY : detection_processor/fast

#=============================================================================
# Target rules for targets named distance_detection

# Build rule for target.
distance_detection: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 distance_detection
.PHONY : distance_detection

# fast build rule for target.
distance_detection/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/distance_detection.dir/build.make CMakeFiles/distance_detection.dir/build
.PHONY : distance_detection/fast

src/app_config.o: src/app_config.cpp.o
.PHONY : src/app_config.o

# target to build an object file
src/app_config.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/app_config.cpp.o
.PHONY : src/app_config.cpp.o

src/app_config.i: src/app_config.cpp.i
.PHONY : src/app_config.i

# target to preprocess a source file
src/app_config.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/app_config.cpp.i
.PHONY : src/app_config.cpp.i

src/app_config.s: src/app_config.cpp.s
.PHONY : src/app_config.s

# target to generate assembly for a file
src/app_config.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/app_config.cpp.s
.PHONY : src/app_config.cpp.s

src/camera_intrinsics.o: src/camera_intrinsics.cpp.o
.PHONY : src/camera_intrinsics.o

# target to build an object file
src/camera_intrinsics.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o
.PHONY : src/camera_intrinsics.cpp.o

src/camera_intrinsics.i: src/camera_intrinsics.cpp.i
.PHONY : src/camera_intrinsics.i

# target to preprocess a source file
src/camera_intrinsics.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.i
.PHONY : src/camera_intrinsics.cpp.i

src/camera_intrinsics.s: src/camera_intrinsics.cpp.s
.PHONY : src/camera_intrinsics.s

# target to generate assembly for a file
src/camera_intrinsics.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.s
.PHONY : src/camera_intrinsics.cpp.s

src/detection_processor.o: src/detection_processor.cpp.o
.PHONY : src/detection_processor.o

# target to build an object file
src/detection_processor.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o
.PHONY : src/detection_processor.cpp.o

src/detection_processor.i: src/detection_processor.cpp.i
.PHONY : src/detection_processor.i

# target to preprocess a source file
src/detection_processor.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/detection_processor.cpp.i
.PHONY : src/detection_processor.cpp.i

src/detection_processor.s: src/detection_processor.cpp.s
.PHONY : src/detection_processor.s

# target to generate assembly for a file
src/detection_processor.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/detection_processor.cpp.s
.PHONY : src/detection_processor.cpp.s

src/logger.o: src/logger.cpp.o
.PHONY : src/logger.o

# target to build an object file
src/logger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/logger.cpp.o
.PHONY : src/logger.cpp.o

src/logger.i: src/logger.cpp.i
.PHONY : src/logger.i

# target to preprocess a source file
src/logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/logger.cpp.i
.PHONY : src/logger.cpp.i

src/logger.s: src/logger.cpp.s
.PHONY : src/logger.s

# target to generate assembly for a file
src/logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/logger.cpp.s
.PHONY : src/logger.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/distance_detection.dir/build.make CMakeFiles/distance_detection.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/distance_detection.dir/build.make CMakeFiles/distance_detection.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/distance_detection.dir/build.make CMakeFiles/distance_detection.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/pixel_converter.o: src/pixel_converter.cpp.o
.PHONY : src/pixel_converter.o

# target to build an object file
src/pixel_converter.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o
.PHONY : src/pixel_converter.cpp.o

src/pixel_converter.i: src/pixel_converter.cpp.i
.PHONY : src/pixel_converter.i

# target to preprocess a source file
src/pixel_converter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.i
.PHONY : src/pixel_converter.cpp.i

src/pixel_converter.s: src/pixel_converter.cpp.s
.PHONY : src/pixel_converter.s

# target to generate assembly for a file
src/pixel_converter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.s
.PHONY : src/pixel_converter.cpp.s

src/simple_json.o: src/simple_json.cpp.o
.PHONY : src/simple_json.o

# target to build an object file
src/simple_json.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/simple_json.cpp.o
.PHONY : src/simple_json.cpp.o

src/simple_json.i: src/simple_json.cpp.i
.PHONY : src/simple_json.i

# target to preprocess a source file
src/simple_json.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/simple_json.cpp.i
.PHONY : src/simple_json.cpp.i

src/simple_json.s: src/simple_json.cpp.s
.PHONY : src/simple_json.s

# target to generate assembly for a file
src/simple_json.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/src/simple_json.cpp.s
.PHONY : src/simple_json.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... detection_processor"
	@echo "... distance_detection"
	@echo "... src/app_config.o"
	@echo "... src/app_config.i"
	@echo "... src/app_config.s"
	@echo "... src/camera_intrinsics.o"
	@echo "... src/camera_intrinsics.i"
	@echo "... src/camera_intrinsics.s"
	@echo "... src/detection_processor.o"
	@echo "... src/detection_processor.i"
	@echo "... src/detection_processor.s"
	@echo "... src/logger.o"
	@echo "... src/logger.i"
	@echo "... src/logger.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/pixel_converter.o"
	@echo "... src/pixel_converter.i"
	@echo "... src/pixel_converter.s"
	@echo "... src/simple_json.o"
	@echo "... src/simple_json.i"
	@echo "... src/simple_json.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system


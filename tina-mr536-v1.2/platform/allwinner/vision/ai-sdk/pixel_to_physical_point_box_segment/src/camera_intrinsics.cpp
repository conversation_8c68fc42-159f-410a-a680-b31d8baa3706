#include "../include/camera_intrinsics.h"
#include <iostream>
#include <stdexcept>

CameraIntrinsics::CameraIntrinsics()
    : fx_(0.0)
    , fy_(0.0)
    , cx_(0.0)
    , cy_(0.0)
    , image_width_(0)
    , image_height_(0)
    , is_initialized_(false)
{
}

bool CameraIntrinsics::loadFromYamlFile(const std::string& yaml_file) {
#ifdef HAVE_YAML_CPP
    try {
        YAML::Node config = YAML::LoadFile(yaml_file);
        return loadFromYamlNode(config);
    } catch (const std::exception& e) {
        LOG_ERROR("加载相机内参文件失败:", e.what());
        return false;
    }
#else
    LOG_ERROR("未编译yaml-cpp支持，无法加载YAML文件");
    return false;
#endif
}

bool CameraIntrinsics::loadFromJsonFile(const std::string& json_file) {
    try {
        SimpleJson config = SimpleJson::parseFile(json_file);
        return loadFromJsonNode(config);
    } catch (const std::exception& e) {
        LOG_ERROR("加载相机内参JSON文件失败:", e.what());
        return false;
    }
}

#ifdef HAVE_YAML_CPP
bool CameraIntrinsics::loadFromYamlNode(const YAML::Node& node) {
    try {
        // 检查必要的节点是否存在
        if (!node["camera_parameters"]) {
            LOG_ERROR("YAML文件中缺少camera_parameters节点");
            return false;
        }

        const YAML::Node& camera_params = node["camera_parameters"];

        // 加载相机矩阵
        if (camera_params["camera_matrix"]) {
            const YAML::Node& camera_matrix = camera_params["camera_matrix"];
            fx_ = camera_matrix["fx"].as<double>();
            fy_ = camera_matrix["fy"].as<double>();
            cx_ = camera_matrix["cx"].as<double>();
            cy_ = camera_matrix["cy"].as<double>();

            LOG_INFO("相机内参加载成功: fx=", fx_, ", fy=", fy_, ", cx=", cx_, ", cy=", cy_);
        } else {
            LOG_ERROR("YAML文件中缺少camera_matrix节点");
            return false;
        }

        // 加载畸变系数
        if (camera_params["distortion_coefficients"]) {
            const YAML::Node& distortion = camera_params["distortion_coefficients"];
            distortion_coeffs_.clear();

            // 径向畸变系数
            if (distortion["radial"]) {
                const YAML::Node& radial = distortion["radial"];
                distortion_coeffs_.push_back(radial["k1"].as<double>());
                distortion_coeffs_.push_back(radial["k2"].as<double>());
                distortion_coeffs_.push_back(radial["k3"].as<double>());
            }

            // 切向畸变系数
            if (distortion["tangential"]) {
                const YAML::Node& tangential = distortion["tangential"];
                distortion_coeffs_.push_back(tangential["p1"].as<double>());
                distortion_coeffs_.push_back(tangential["p2"].as<double>());
            }

            LOG_INFO("畸变系数加载成功，共", distortion_coeffs_.size(), "个参数");
        }

        // 加载图像属性
        if (node["image_properties"]) {
            const YAML::Node& image_props = node["image_properties"];
            image_width_ = image_props["width"].as<int>();
            image_height_ = image_props["height"].as<int>();

            LOG_INFO("图像属性: 宽度=", image_width_, ", 高度=", image_height_);
        } else {
            LOG_WARNING("YAML文件中缺少image_properties节点，使用默认值");
            image_width_ = 1280;
            image_height_ = 720;
        }

        is_initialized_ = true;
        LOG_INFO("相机内参初始化完成");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR("解析YAML节点失败:", e.what());
        return false;
    }
}
#endif

bool CameraIntrinsics::loadFromJsonNode(const SimpleJson& json) {
    try {
        // 检查必要的节点是否存在
        if (!json.hasKey("camera_parameters")) {
            LOG_ERROR("JSON文件中缺少camera_parameters节点");
            return false;
        }

        const SimpleJson& camera_params = json["camera_parameters"];

        // 加载相机矩阵
        if (camera_params.hasKey("camera_matrix")) {
            const SimpleJson& camera_matrix = camera_params["camera_matrix"];
            fx_ = camera_matrix["fx"].asDouble();
            fy_ = camera_matrix["fy"].asDouble();
            cx_ = camera_matrix["cx"].asDouble();
            cy_ = camera_matrix["cy"].asDouble();

            LOG_INFO("相机内参加载成功: fx=", fx_, ", fy=", fy_, ", cx=", cx_, ", cy=", cy_);
        } else {
            LOG_ERROR("JSON文件中缺少camera_matrix节点");
            return false;
        }

        // 加载畸变系数
        if (camera_params.hasKey("distortion_coefficients")) {
            const SimpleJson& distortion = camera_params["distortion_coefficients"];
            distortion_coeffs_.clear();

            // 径向畸变系数
            if (distortion.hasKey("radial")) {
                const SimpleJson& radial = distortion["radial"];
                distortion_coeffs_.push_back(radial["k1"].asDouble());
                distortion_coeffs_.push_back(radial["k2"].asDouble());
                distortion_coeffs_.push_back(radial["k3"].asDouble());
            }

            // 切向畸变系数
            if (distortion.hasKey("tangential")) {
                const SimpleJson& tangential = distortion["tangential"];
                distortion_coeffs_.push_back(tangential["p1"].asDouble());
                distortion_coeffs_.push_back(tangential["p2"].asDouble());
            }

            LOG_INFO("畸变系数加载成功，共", distortion_coeffs_.size(), "个参数");
        }

        // 加载图像属性
        if (json.hasKey("image_properties")) {
            const SimpleJson& image_props = json["image_properties"];
            image_width_ = image_props["width"].asInt();
            image_height_ = image_props["height"].asInt();

            LOG_INFO("图像属性: 宽度=", image_width_, ", 高度=", image_height_);
        } else {
            LOG_WARNING("JSON文件中缺少image_properties节点，使用默认值");
            image_width_ = 1280;
            image_height_ = 720;
        }

        is_initialized_ = true;
        LOG_INFO("相机内参初始化完成");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR("解析JSON节点失败:", e.what());
        return false;
    }
}

std::tuple<double, double, double> CameraIntrinsics::pixelToCamera(int pixel_x, int pixel_y, double depth) const {
    if (!is_initialized_) {
        LOG_ERROR("相机内参未初始化");
        return std::make_tuple(0.0, 0.0, 0.0);
    }
    
    // 像素坐标转换为相机坐标系
    // 相机坐标系公式：
    // X = (u - cx) * Z / fx
    // Y = (v - cy) * Z / fy
    // Z = depth
    
    double camera_x = (pixel_x - cx_) * depth / fx_;
    double camera_y = (pixel_y - cy_) * depth / fy_;
    double camera_z = depth;
    
    LOG_DEBUG("像素坐标(", pixel_x, ",", pixel_y, ") 深度=", depth, 
              " -> 相机坐标(", camera_x, ",", camera_y, ",", camera_z, ")");
    
    return std::make_tuple(camera_x, camera_y, camera_z);
}

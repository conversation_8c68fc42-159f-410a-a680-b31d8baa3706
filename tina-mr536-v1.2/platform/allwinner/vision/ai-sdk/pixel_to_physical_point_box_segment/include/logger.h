#ifndef LOGGER_H
#define LOGGER_H

#include <iostream>
#include <string>
#include <sstream>

/**
 * @brief 简单的日志管理系统
 * 
 * 提供不同级别的日志输出，可以通过编译时宏控制是否输出调试信息
 */
class Logger {
public:
    enum Level {
        DEBUG = 0,
        INFO = 1,
        WARNING = 2,
        ERROR = 3
    };

    /**
     * @brief 设置日志级别
     * @param level 日志级别，只有大于等于此级别的日志才会输出
     */
    static void setLevel(Level level) {
        current_level_ = level;
    }

    /**
     * @brief 获取当前日志级别
     */
    static Level getLevel() {
        return current_level_;
    }

    /**
     * @brief 输出调试信息
     */
    template<typename... Args>
    static void debug(Args&&... args) {
        if (current_level_ <= DEBUG) {
            log("[DEBUG]", std::forward<Args>(args)...);
        }
    }

    /**
     * @brief 输出信息
     */
    template<typename... Args>
    static void info(Args&&... args) {
        if (current_level_ <= INFO) {
            log("[INFO]", std::forward<Args>(args)...);
        }
    }

    /**
     * @brief 输出警告
     */
    template<typename... Args>
    static void warning(Args&&... args) {
        if (current_level_ <= WARNING) {
            log("[WARNING]", std::forward<Args>(args)...);
        }
    }

    /**
     * @brief 输出错误
     */
    template<typename... Args>
    static void error(Args&&... args) {
        if (current_level_ <= ERROR) {
            log("[ERROR]", std::forward<Args>(args)...);
        }
    }

private:
    static Level current_level_;

    template<typename... Args>
    static void log(const std::string& prefix, Args&&... args) {
        std::ostringstream oss;
        oss << prefix << " ";
        ((oss << args << " "), ...);
        std::cout << oss.str() << std::endl;
    }
};

// 编译时控制调试输出的宏
#ifdef NDEBUG
    #define LOG_DEBUG(...)
    #define LOG_INFO(...)
    #define LOG_WARNING(...) Logger::warning(__VA_ARGS__)
    #define LOG_ERROR(...) Logger::error(__VA_ARGS__)
#else
    #define LOG_DEBUG(...) Logger::debug(__VA_ARGS__)
    #define LOG_INFO(...) Logger::info(__VA_ARGS__)
    #define LOG_WARNING(...) Logger::warning(__VA_ARGS__)
    #define LOG_ERROR(...) Logger::error(__VA_ARGS__)
#endif

#endif // LOGGER_H

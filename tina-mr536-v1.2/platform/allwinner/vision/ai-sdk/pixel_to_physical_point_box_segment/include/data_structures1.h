#ifndef DATA_STRUCTURES_H
#define DATA_STRUCTURES_H

#include <string>

// 相机坐标系点结构体
struct CameraPoint {
    double x, y, z;

    CameraPoint() : x(0.0), y(0.0), z(0.0) {}
    CameraPoint(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
};

// 相机坐标系结构体 - 存储bbox的相机坐标信息
struct CameraCoordinates {
    CameraPoint bottom_left; // 左下角相机坐标
    CameraPoint bottom_right; // 右下角相机坐标

    CameraCoordinates() = default;

    CameraCoordinates(
                     const CameraPoint& bottom_left_,
                     const CameraPoint& bottom_right_)
   
        : bottom_right(bottom_left_)
        , bottom_left(bottom_right_)
    {}
};

// 检测框结构体 - 对应Python的BBox dataclass
struct BBox {
    float xmin;
    float ymin;
    float xmax;
    float ymax;
    int label;
    float score;

    BBox() : xmin(0.0f), ymin(0.0f), xmax(0.0f), ymax(0.0f), label(0), score(0.0f) {}
    BBox(float xmin_, float ymin_, float xmax_, float ymax_, int label_, float score_)
        : xmin(xmin_), ymin(ymin_), xmax(xmax_), ymax(ymax_), label(label_), score(score_) {}
};

// 检测结果结构体 - 对应Python的DetectionResult dataclass
struct DetectionResult {
    BBox bbox;  // 包含坐标、标签和置信度
    bool flag;
    double physical_distance;
    double left_distance;
    double right_distance;
    double length;
    // 相机坐标系信息
    CameraCoordinates camera_coords;

    DetectionResult()
        : flag(1)
        , physical_distance(0.0)
        , left_distance(0.0)
        , right_distance(0.0)
        , length(0.0)
        , camera_coords()
    {}

    DetectionResult(const BBox& bbox_, bool flag_)
        : bbox(bbox_)
        , flag(flag_)
        , physical_distance(0.0)
        , left_distance(0.0)
        , right_distance(0.0)
        , length(0.0)
        , camera_coords()
    {}
};



#endif // DATA_STRUCTURES_H

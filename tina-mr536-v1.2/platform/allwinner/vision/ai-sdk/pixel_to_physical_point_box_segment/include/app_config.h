#ifndef APP_CONFIG_H
#define APP_CONFIG_H

#include <string>
#include <vector>
#include <utility>
#include "simple_json.h"

class AppConfig {
public:
    // Constructor with default values
    AppConfig();

    // Constructor that loads from JSON file
    AppConfig(const std::string& json_file_path);

    // Load configuration from JSON file
    bool loadFromJson(const std::string& json_file_path);
    
    // Configuration parameters
    
    std::string size_ranges_config;
    std::string distance_table_path;
    std::string calib_intrix_path;
    
    // Constants from pixel_converter.py
    std::pair<int, int> imgx_range;  // IMGX_RANGE
    std::pair<int, int> imgy_range;  // IMGY_RANGE
    int X_MIN;
    int X_MAX;
    int Y_MIN;
    int Y_MAX;
    
    // // Image dimensions
    // std::pair<int, int> img_wh;
    
    // Class names
    std::vector<std::string> class_names;

    // Wire detection grouping parameters
    int window_size;
    double max_z_diff_threshold;
    double pixel_gap_threshold;

    // Getters for convenience
    int getTableWidth() const;
    int getTableHeight() const;
};

#endif // APP_CONFIG_H

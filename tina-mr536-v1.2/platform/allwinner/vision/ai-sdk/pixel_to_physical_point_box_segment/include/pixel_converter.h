#ifndef PIXEL_CONVERTER_H
#define PIXEL_CONVERTER_H

#include <string>
#include <iostream>
#include <fstream>
#include <vector>
#include <utility>
#include <memory>
#include "app_config.h"
// #include "error_handling.h"

// // OpenCV头文件 (可选，根据需要包含)
// #ifdef USE_OPENCV
// #include <opencv2/opencv.hpp>
// #include <opencv2/core.hpp>
// #include <opencv2/imgproc.hpp>
// #endif

/**
 * @brief 像素坐标到物理坐标转换器
 *
 * PixelConverter类提供像素坐标到物理坐标的转换功能。它基于预先计算的
 * 距离表文件，使用双线性插值算法实现高精度的坐标转换。
 *
 * 核心功能：
 * - 加载和解析距离表文件
 * - 像素坐标有效性验证
 * - 双线性插值坐标转换
 * - 配置参数管理
 *
 * 距离表格式：
 * 距离表是一个二维数组，每个元素包含对应像素点的物理坐标信息。
 * 文件格式为二进制或文本格式，包含X和Y方向的物理坐标值。
 *
 * 坐标系定义：
 * - 输入：图像像素坐标（左上角为原点，X向右，Y向下）
 * - 输出：物理坐标（设备坐标系，单位厘米）
 *
 * @note 该类是线程安全的，可以在多线程环境中使用
 * @see AppConfig 配置管理类
 * @see DetectionProcessor 上层处理类
 *
 * 典型用法：
 * @code
 * auto config = std::make_shared<AppConfig>();
 * PixelConverter converter("distance_table.bin", config);
 *
 * if (converter.isValidPixelCoordinate(x, y)) {
 *     auto result = converter.queryPhysicalLocationSafe(x, y);
 *     if (ErrorHandling::isSuccess(result)) {
 *         auto [phys_x, phys_y] = ErrorHandling::getValue(result);
 *         // 使用物理坐标
 *     }
 * }
 * @endcode
 *
 * @warning 使用前必须确保距离表文件存在且格式正确
 */
class PixelConverter {
public:
    /**
     * @brief 构造函数
     *
     * 创建PixelConverter实例并初始化距离表。构造函数会自动加载
     * 指定路径的距离表文件，并从配置对象中获取必要的参数。
     *
     * @param table_path 距离表文件的完整路径
     * @param config 应用配置对象，包含图像尺寸、坐标范围等参数
     *
     * @throws std::runtime_error 当距离表文件不存在或格式错误时
     * @throws std::invalid_argument 当配置参数无效时
     *
     * @note 距离表文件必须与配置参数匹配，否则转换结果可能不准确
     */
    explicit PixelConverter(const std::string& table_path, std::shared_ptr<AppConfig> config = nullptr);

    /**
     * @brief 查询物理位置（异常版本）
     *
     * 将像素坐标转换为物理坐标。如果转换失败，会抛出异常。
     *
     * @param pixel_x 像素X坐标
     * @param pixel_y 像素Y坐标
     * @return 物理坐标对(x, y)，单位为厘米
     *
     * @throws std::out_of_range 当像素坐标超出有效范围时
     * @throws std::runtime_error 当距离表数据损坏时
     *
     * @deprecated 推荐使用queryPhysicalLocationSafe()以获得更好的错误处理
     */
    std::pair<double, double> queryPhysicalLocation(int pixel_x, int pixel_y);

    // /**
    //  * @brief 查询物理位置（安全版本）
    //  *
    //  * 将像素坐标转换为物理坐标，使用Result类型进行错误处理。
    //  * 这是推荐的转换方法，提供更好的错误信息和异常安全性。
    //  *
    //  * @param pixel_x 像素X坐标（图像坐标系）
    //  * @param pixel_y 像素Y坐标（图像坐标系）
    //  * @return 转换结果，成功时包含物理坐标对，失败时包含错误信息
    //  *
    //  * @retval std::pair<double, double> 物理坐标(x, y)，单位为厘米
    //  * @retval ErrorInfo 转换失败的详细错误信息
    //  *
    //  * @note 转换算法使用双线性插值以提高精度
    //  */
    // ErrorHandling::Result<std::pair<double, double>> queryPhysicalLocationSafe(int pixel_x, int pixel_y) const;

    /**
     * @brief 验证像素坐标是否在有效范围内
     *
     * 检查给定的像素坐标是否在配置的有效区域内，以及是否在
     * 距离表覆盖的范围内。
     *
     * @param pixel_x 像素X坐标
     * @param pixel_y 像素Y坐标
     * @return true 坐标有效，false 坐标无效
     *
     * @note 有效性检查包括：
     *       - 坐标是否在图像边界内
     *       - 坐标是否在配置的有效区域内
     *       - 距离表是否包含该坐标的数据
     */
    bool isValidPixelCoordinate(int pixel_x, int pixel_y) const;

    /**
     * @brief 获取图像X坐标范围
     * @return X坐标范围对(最小值, 最大值)
     */
    std::pair<int, int> getImgxRange() const;

    /**
     * @brief 获取图像Y坐标范围
     * @return Y坐标范围对(最小值, 最大值)
     */
    std::pair<int, int> getImgyRange() const;

    /**
     * @brief 获取物理坐标X的最小值
     * @return X坐标最小值（厘米）
     */
    int getXMin() const;

    /**
     * @brief 获取物理坐标X的最大值
     * @return X坐标最大值（厘米）
     */
    int getXMax() const;

    /**
     * @brief 获取物理坐标Y的最小值
     * @return Y坐标最小值（厘米）
     */
    int getYMin() const;

    /**
     * @brief 获取物理坐标Y的最大值
     * @return Y坐标最大值（厘米）
     */
    int getYMax() const;

private:
    std::string table_path_;                                    ///< 距离表文件路径
    std::shared_ptr<AppConfig> config_;                         ///< 应用配置对象
    std::vector<std::pair<uint8_t, uint8_t>> table_data_;      ///< 距离表数据（压缩格式）

    // 从配置中获取的常量
    std::pair<int, int> IMGX_RANGE;     ///< 图像X坐标有效范围
    std::pair<int, int> IMGY_RANGE;     ///< 图像Y坐标有效范围
    int X_MIN, X_MAX, Y_MIN, Y_MAX;     ///< 物理坐标范围边界
    int TABLE_WIDTH, TABLE_HEIGHT;     ///< 距离表的宽度和高度

    /**
     * @brief 加载距离表文件
     *
     * 从指定路径加载距离表文件到内存中。距离表包含每个像素点
     * 对应的物理坐标信息，以压缩格式存储。
     *
     * @throws std::runtime_error 当文件不存在、格式错误或读取失败时
     *
     * @note 距离表格式：
     *       - 每个像素点对应一个坐标对(x, y)
     *       - 坐标值被压缩到0-255范围内
     *       - 文件大小应为 TABLE_WIDTH * TABLE_HEIGHT * 2 字节
     */
    void loadTable();

    /**
     * @brief 将压缩值映射回实际物理范围
     *
     * 将距离表中的0-255压缩值转换回实际的物理坐标值。
     *
     * @param value 压缩值（0-255）
     * @param min_val 物理坐标的最小值
     * @param max_val 物理坐标的最大值
     * @return 映射后的实际物理坐标值
     *
     * @note 映射公式：actual_value = min_val + (value / 255.0) * (max_val - min_val)
     */
    static double mapValueBack(uint8_t value, double min_val, double max_val);
};

#endif // PIXEL_CONVERTER_H